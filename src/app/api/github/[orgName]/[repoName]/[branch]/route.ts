
import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import type { JWT } from "next-auth/jwt";
import { getToken } from "next-auth/jwt";

const secret = process.env.NEXTAUTH_SECRET;

type Params = {
  orgName: string;
  repoName: string;
  branch: string;
};

export async function GET(req: NextRequest, { params }: { params: Promise<Params> }) {
    const sesh = await getServerSession();
    if (!sesh) {
      return NextResponse.json({ error: "Need Log in" }, { status: 401 });
    }
    
    // It's guaranteed to be there due to the check above
    const token: JWT = (await getToken({ req, secret })) as JWT;

    const { orgName, repoName, branch } = await params;
    
    try {
    const foundData = await axios({
      method: "GET",
      url: `${process.env.BACKEND_API_URL}/github/${orgName}/${repoName}/${branch}`,
      headers: { Authorization: `Bearer ${token.access_token}` },
    });
  
      return NextResponse.json(foundData.data);
  } catch (e) {
    return NextResponse.json({ error: "Failed to get github" }, { status: 500 });
  }
}
  
import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

const secret = process.env.NEXTAUTH_SECRET;

// Id is used as the ID of the share in this case
export async function GET(req: NextRequest, context: any) {
  const { id } = await context.params;

  if (!id) {
    return NextResponse.json(
      { data: {}, message: "Need to pass a shareId" },
      { status: 401 }
    );
  }

  let foundData;
  try {
    foundData = await axios({
      method: "GET",
      url: `${process.env.BACKEND_API_URL}/shares/${id}`,
    });
  } catch (e) {
    // Axios error handling
    if (e?.response?.data) {
      return NextResponse.json(
        { data: {}, message: e.response.data.message },
        { status: e.response.status }
      );
    } else {
      return NextResponse.json(
        { data: {}, message: "Something went wrong" },
        { status: 500 }
      );
    }
  }

  // Returns an object with {data, message}
  return NextResponse.json(foundData.data);
}

"use client";

import { useEffect, useState } from "react";
import { FiRefreshCw, FiSettings } from "react-icons/fi";

import type { ENV_TYPE } from "@/app/app.constants";
import { AppButton } from "@/app/components/app-button";
import { Body3, Body4, H5 } from "@/app/components/app-typography";
import type { GlobalVmConfig } from "@/app/utils/vm-config";
import {
  getCurrentVmConfig,
  getToolDefaults,
  isUsingToolDefaults,
  resetToToolDefaults,
  updateVmConfig,
} from "@/app/utils/vm-config";

interface GlobalVmConfigProps {
  tool: ENV_TYPE;
  onConfigChange?: (config: GlobalVmConfig) => void;
  className?: string;
}

export function GlobalVmConfigComponent({
  tool,
  onConfigChange,
  className = "",
}: GlobalVmConfigProps) {
  const [config, setConfig] = useState<GlobalVmConfig>({
    prank: false,
    roll: false,
    time: false,
  });
  const [isUsingDefaults, setIsUsingDefaults] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);

  // Load configuration on mount and when tool changes
  useEffect(() => {
    const currentConfig = getCurrentVmConfig(tool);
    setConfig(currentConfig);
    setIsUsingDefaults(currentConfig.isUsingDefaults);
  }, [tool]);

  const handleToggle = (property: keyof GlobalVmConfig) => {
    const newConfig = updateVmConfig(config, {
      [property]: !config[property],
    });

    setConfig(newConfig);
    setIsUsingDefaults(isUsingToolDefaults(newConfig, tool));

    if (onConfigChange) {
      onConfigChange(newConfig);
    }
  };

  // Listen for external config changes (e.g., from VM buttons in log parser)
  useEffect(() => {
    const handleConfigChange = (event: CustomEvent) => {
      const newConfig = event.detail as GlobalVmConfig;
      setConfig(newConfig);
      setIsUsingDefaults(isUsingToolDefaults(newConfig, tool));
    };

    const handleStorageChange = () => {
      const currentConfig = getCurrentVmConfig(tool);
      setConfig(currentConfig);
      setIsUsingDefaults(currentConfig.isUsingDefaults);
    };

    window.addEventListener(
      "vm-config-changed",
      handleConfigChange as EventListener
    );
    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener(
        "vm-config-changed",
        handleConfigChange as EventListener
      );
      window.removeEventListener("storage", handleStorageChange);
    };
  }, [tool]);

  const handleResetToDefaults = () => {
    const defaults = resetToToolDefaults(tool);
    setConfig(defaults);
    setIsUsingDefaults(true);

    if (onConfigChange) {
      onConfigChange(defaults);
    }
  };

  const getToolDisplayName = (tool: ENV_TYPE): string => {
    switch (tool) {
      case "ECHIDNA":
        return "Echidna";
      case "MEDUSA":
        return "Medusa";
      case "HALMOS":
        return "Halmos";
      case "FOUNDRY":
        return "Foundry";
      case "KONTROL":
        return "Kontrol";
      default:
        return tool;
    }
  };

  const toolDefaults = getToolDefaults(tool);

  return (
    <div
      className={`rounded-lg border border-stroke-neutral-decorative bg-back-neutral-secondary p-4 ${className}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FiSettings className="size-4 text-fore-neutral-secondary" />
          <H5 className="text-fore-neutral-primary">VM Configuration</H5>
          {isUsingDefaults && (
            <Body4 className="text-fore-neutral-secondary">
              (Using {getToolDisplayName(tool)} defaults)
            </Body4>
          )}
        </div>
        <div className="flex items-center gap-2">
          <AppButton
            variant="outline"
            size="sm"
            onClick={handleResetToDefaults}
            leftIcon={<FiRefreshCw className="size-3" />}
          >
            Reset to defaults
          </AppButton>
          <AppButton
            variant="outline"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? "Hide" : "Show"}
          </AppButton>
        </div>
      </div>

      {isExpanded && (
        <div className="mt-4 space-y-3">
          <Body3 className="text-fore-neutral-secondary">
            Global VM configuration applies to all reproduction code generation.
            These settings persist across browser sessions.
          </Body3>

          <div className="grid grid-cols-1 gap-3 sm:grid-cols-3">
            <VmToggleButton
              label="vm.prank"
              description="Use vm.prank for msg.sender manipulation"
              isEnabled={config.prank}
              isDefault={toolDefaults.prank}
              onClick={() => handleToggle("prank")}
            />
            <VmToggleButton
              label="vm.roll"
              description="Use vm.roll for block.number manipulation"
              isEnabled={config.roll}
              isDefault={toolDefaults.roll}
              onClick={() => handleToggle("roll")}
            />
            <VmToggleButton
              label="vm.warp"
              description="Use vm.warp for block.timestamp manipulation"
              isEnabled={config.time}
              isDefault={toolDefaults.time}
              onClick={() => handleToggle("time")}
            />
          </div>

          <div className="mt-4 rounded-md bg-back-neutral-tertiary p-3">
            <Body4 className="text-fore-neutral-secondary">
              <strong>Tool Defaults:</strong>
              <br />
              • Echidna: All VM options enabled (comprehensive reproduction)
              <br />• Medusa/Halmos: All VM options disabled (minimal
              reproduction)
            </Body4>
          </div>
        </div>
      )}
    </div>
  );
}

interface VmToggleButtonProps {
  label: string;
  description: string;
  isEnabled: boolean;
  isDefault: boolean;
  onClick: () => void;
}

function VmToggleButton({
  label,
  description,
  isEnabled,
  isDefault,
  onClick,
}: VmToggleButtonProps) {
  return (
    <div className="rounded-md border border-stroke-neutral-decorative bg-back-neutral-primary p-3">
      <div className="flex items-center justify-between mb-2">
        <Body3 className="font-medium text-fore-neutral-primary">{label}</Body3>
        <div className="flex items-center gap-2">
          {isDefault && (
            <Body4 className="text-xs text-fore-neutral-secondary">
              default
            </Body4>
          )}
          <AppButton
            variant={isEnabled ? "primary" : "outline"}
            size="xs"
            onClick={onClick}
          >
            {isEnabled ? "ON" : "OFF"}
          </AppButton>
        </div>
      </div>
      <Body4 className="text-fore-neutral-secondary text-xs">
        {description}
      </Body4>
    </div>
  );
}

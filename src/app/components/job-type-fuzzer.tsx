"use client";

import { cn } from "../helpers/cn";
import { ENV_TYPE } from "../app.constants";
import { AppButton } from "./app-button";
import { H3, H5, H6 } from "./app-typography";

type JobTypeFuzzerProps = {
  value: ENV_TYPE;
  onChange: (value: ENV_TYPE) => void;
  className?: string;
};

const jobTypeOptions = [
  { label: "Medusa", value: ENV_TYPE.MEDUSA },
  { label: "Echidna", value: ENV_TYPE.ECHIDNA },
  { label: "Foundry", value: ENV_TYPE.FOUNDRY },
  { label: "Halmos", value: ENV_TYPE.HALMOS },
  { label: "Kontrol", value: ENV_TYPE.KONTROL },
];

export const JobTypeFuzzer = ({
  value,
  onChange,
  className,
}: JobTypeFuzzerProps) => {
  return (
    <div
      className={cn(
        "flex flex-col gap-3 pb-4 border-b border-back-neutral-tertiary",
        className
      )}
    >
      <div className="flex items-center gap-4">
        <H6>Select Job Type:</H6>

        <div className="flex flex-row flex-wrap gap-3">
          {jobTypeOptions.map((option) => {
            const isSelected = value === option.value;

            return (
              <AppButton
                key={option.value}
                onClick={() => onChange(option.value)}
                variant={isSelected ? "primary" : "outline"}
              >
                {option.label}
              </AppButton>
            );
          })}
        </div>
      </div>
    </div>
  );
};

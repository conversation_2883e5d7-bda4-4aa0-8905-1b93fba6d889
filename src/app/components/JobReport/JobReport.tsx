import { sortProperties } from "@/app/(app)/tools/mdReportHelper";
import type { FuzzingResults } from "@recon-fuzz/log-parser";
import React from "react";
import { H2, H3, Body1, Body2, Body3 } from "@/app/components/app-typography";
import { FaCheckCircle, FaTimesCircle, FaChartLine } from "react-icons/fa";

interface JobReportProps {
  fuzzer: string;
  jobStats: FuzzingResults;
  showBrokenProp: boolean;
}

export default function JobReport({
  fuzzer,
  jobStats,
  showBrokenProp,
}: JobReportProps) {
  const successRate =
    jobStats.numberOfTests > 0
      ? Math.round((jobStats.passed / jobStats.numberOfTests) * 100)
      : 0;

  return (
    <div className="w-full rounded-xl border border-stroke-neutral-decorative bg-back-neutral-tertiary p-6">
      <div className="mb-6 flex items-start justify-between">
        <div className="flex-1">
          <H2 className="mb-2 text-fore-neutral-primary">Fuzzing Report</H2>
          <Body1 color="secondary" className="text-fore-neutral-secondary">
            {fuzzer} • {jobStats.duration}s runtime
          </Body1>
        </div>
        <div className="ml-6 text-right">
          <div
            className={`mb-1 text-3xl font-bold leading-none ${
              successRate >= 80
                ? "text-status-success"
                : successRate >= 50
                  ? "text-accent-primary"
                  : "text-status-error"
            }`}
          >
            {successRate}%
          </div>
          <Body2 color="secondary">Success Rate</Body2>
        </div>
      </div>

      <div className="mb-6 flex items-center justify-between rounded-lg border border-stroke-neutral-decorative bg-back-neutral-primary p-4">
        <div className="flex items-center gap-3">
          <FaChartLine className="text-accent-primary" size={20} />
          <div>
            <Body3 color="secondary" className="text-fore-neutral-secondary">
              Total Tests
            </Body3>
            <H3 className="text-fore-neutral-primary">
              {jobStats.numberOfTests}
            </H3>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <FaCheckCircle className="text-status-success" size={20} />
          <div>
            <Body3 color="secondary" className="text-fore-neutral-secondary">
              Passed
            </Body3>
            <H3 className="text-status-success">{jobStats.passed}</H3>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <FaTimesCircle className="text-status-error" size={20} />
          <div>
            <Body3 color="secondary" className="text-fore-neutral-secondary">
              Failed
            </Body3>
            <H3 className="text-status-error">{jobStats.failed}</H3>
          </div>
        </div>
      </div>

      {showBrokenProp && jobStats.brokenProperties?.length > 0 && (
        <div className="mb-5">
          <div className="mb-3 flex items-center gap-2">
            <FaTimesCircle className="text-status-error" size={18} />
            <H3 className="text-fore-neutral-primary">
              Broken Properties ({jobStats.brokenProperties.length})
            </H3>
          </div>
          <div className="space-y-2">
            {jobStats.brokenProperties.slice(0, 4).map((el, index) => (
              <div
                key={`${el.brokenProperty}-${index}`}
                className="bg-status-error/10 border-status-error/20 flex items-start gap-3 rounded-lg border p-3"
              >
                <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-status-error text-sm font-bold text-white">
                  {index + 1}
                </div>
                <Body1 className="flex-1 text-fore-neutral-primary">
                  {el.brokenProperty}
                </Body1>
              </div>
            ))}
            {jobStats.brokenProperties.length > 4 && (
              <Body2 color="secondary" className="py-2 text-center">
                +{jobStats.brokenProperties.length - 4} more broken properties
              </Body2>
            )}
          </div>
        </div>
      )}

      <div>
        <div className="mb-3 flex items-center gap-2">
          <FaChartLine className="text-accent-primary" size={18} />
          <H3 className="text-fore-neutral-primary">
            Test Results ({jobStats.results.length})
          </H3>
        </div>

        {jobStats.results.length > 0 ? (
          <div className="grid max-h-80 grid-cols-1 gap-3 overflow-y-auto lg:grid-cols-2">
            {sortProperties(jobStats.results).map(
              ({ property, status, index }) => (
                <div
                  key={`${property}-${index}`}
                  className={`rounded-lg border p-3 ${
                    status === "passed"
                      ? "bg-status-success/10 border-status-success/20"
                      : status === "failed"
                        ? "bg-status-error/10 border-status-error/20"
                        : "border-stroke-neutral-decorative bg-back-neutral-primary"
                  }`}
                >
                  <div className="flex items-center justify-between gap-3">
                    <Body1 className="flex-1 truncate font-medium text-fore-neutral-primary">
                      {property}
                    </Body1>
                    <div className="flex items-center gap-2">
                      {status === "passed" ? (
                        <FaCheckCircle
                          className="text-status-success"
                          size={16}
                        />
                      ) : status === "failed" ? (
                        <FaTimesCircle
                          className="text-status-error"
                          size={16}
                        />
                      ) : null}
                      <Body2
                        className={`font-semibold ${
                          status === "passed"
                            ? "text-status-success"
                            : status === "failed"
                              ? "text-status-error"
                              : "text-fore-neutral-secondary"
                        }`}
                      >
                        {status.toUpperCase()}
                      </Body2>
                    </div>
                  </div>
                </div>
              )
            )}
          </div>
        ) : (
          <div className="rounded-lg border border-stroke-neutral-decorative bg-back-neutral-primary py-6 text-center">
            <Body1 color="secondary">No test results available</Body1>
          </div>
        )}
      </div>
    </div>
  );
}

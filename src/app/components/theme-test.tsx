"use client";

import { usePersistedTheme } from "../services/useThemePersistence";
import { THEME_OPTIONS } from "../services/ThemeProvider";

export const ThemeTest = () => {
  const { theme, setTheme, isDark, mounted } = usePersistedTheme();

  return (
    <div className="rounded-lg border p-4">
      <h3 className="mb-2 text-lg font-bold">Theme Test Component</h3>
      <p>Current theme: {theme}</p>
      <p>Is dark: {isDark ? "Yes" : "No"}</p>
      <p>Is mounted: {mounted ? "Yes" : "No"}</p>
      
      <div className="mt-4 space-x-2">
        <button
          onClick={() => setTheme(THEME_OPTIONS.light)}
          className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
        >
          Set Light
        </button>
        <button
          onClick={() => setTheme(THEME_OPTIONS.dark)}
          className="rounded bg-gray-800 px-4 py-2 text-white hover:bg-gray-900"
        >
          Set Dark
        </button>
      </div>
      
      <div className="mt-4 rounded bg-back-neutral-primary p-2 text-fore-neutral-primary">
        This div should change colors based on theme
      </div>
    </div>
  );
};

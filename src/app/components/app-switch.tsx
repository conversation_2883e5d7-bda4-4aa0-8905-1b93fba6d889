"use client";

import noop from "lodash/noop";
import { forwardRef, useCallback, useId } from "react";
import type { IconType } from "react-icons/lib";

import { cn } from "../helpers/cn";
import { Body2 } from "./app-typography";

type AppSwitchProps = {
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  enabled?: boolean;
  disabled?: boolean;
  label?: string;
  containerClassName?: string;
  error?: string;
  disableError?: boolean;
  icon?: IconType;
  tooltip?: string;
  name?: string;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  ref?: React.Ref<HTMLInputElement>;
};

export const AppSwitch = forwardRef<HTMLInputElement, AppSwitchProps>(
  (
    {
      enabled = false,
      onChange = noop,
      className = "",
      disabled = false,
      label,
      containerClassName = "",
      error,
      disableError = false,
      icon: Icon,
      tooltip,
      ...rest
    },
    ref
  ) => {
    const id = useId();

    const onSwitchChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        if (disabled) return;

        onChange(e);
      },
      [onChange, disabled]
    );

    const getSwitchStyles = () => {
      const baseStyles =
        "block h-[20px] w-[36px] rounded-full shadow-inner transition-colors duration-200";

      if (disabled) {
        return cn(baseStyles, "bg-stroke-neutral-decorative opacity-50");
      }

      if (error) {
        return cn(
          baseStyles,
          enabled ? "bg-status-error" : "bg-status-error/30"
        );
      }

      return cn(
        baseStyles,
        enabled ? "bg-accent-primary" : "bg-accent-tertiary"
      );
    };

    const getKnobStyles = () => {
      const baseStyles =
        "dot absolute left-1 top-[3px] size-[14px] rounded-full bg-fore-on-accent-primary transition-all duration-200 shadow";

      if (disabled) {
        return cn(baseStyles, "bg-fore-neutral-quaternary", {
          "translate-x-full": enabled,
          "translate-x-0": !enabled,
        });
      }

      return cn(baseStyles, {
        "translate-x-full border border-accent-primary": enabled,
        "translate-x-0 border border-stroke-neutral-decorative": !enabled,
      });
    };

    return (
      <div
        className={cn("relative flex items-center gap-2", containerClassName)}
      >
        <div className="group relative">
          <label
            className={cn("flex items-center cursor-pointer", className, {
              "cursor-not-allowed": disabled,
            })}
          >
            <div className="relative">
              <input
                id={id}
                ref={ref}
                type="checkbox"
                className="sr-only"
                checked={enabled}
                onChange={onSwitchChange}
                disabled={disabled}
                {...rest}
              />
              <div className={getSwitchStyles()} />
              <div className={getKnobStyles()} />
            </div>
          </label>

          {tooltip && (
            <div className="absolute bottom-[130%] left-1/2  mb-2 hidden w-max max-w-xs -translate-x-1/2 rounded-md border border-stroke-neutral-decorative bg-back-neutral-primary px-3 py-1 text-sm shadow-lg group-hover:block">
              <Body2 color="secondary">{tooltip}</Body2>
            </div>
          )}
        </div>

        {label && (
          <label
            htmlFor={id}
            className={cn("cursor-pointer", {
              "opacity-50 cursor-not-allowed": disabled,
            })}
          >
            <Body2 color="secondary">{label}</Body2>
          </label>
        )}

        {!!Icon && <Icon className="size-4 text-fore-neutral-tertiary" />}

        {!disableError && error && (
          <Body2
            color="secondary"
            className="mt-[3px] block h-[14px] text-status-error"
          >
            {error}
          </Body2>
        )}
      </div>
    );
  }
);

AppSwitch.displayName = "AppSwitch";

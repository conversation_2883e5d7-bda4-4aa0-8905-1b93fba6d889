import { useEffect } from "react";
import { useFormContext } from "react-hook-form";

import { AppInput } from "../app-input";
import { AppSelect } from "../app-select";
import { AppSwitch } from "../app-switch";
import { PreInstallItem } from "./preinstall-item";
import { FORM_STYLES, FORK_MODE_OPTIONS, TEST_MODE_OPTIONS } from "./constants";

export const SubFormEchidna = () => {
  const { register, watch, setValue } = useFormContext();

  const forkMode = watch("forkMode");

  useEffect(() => {}, [forkMode, setValue]);

  return (
    <div className={FORM_STYLES.formContainer}>
      <div className={FORM_STYLES.inputGroup}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Path to test contract"
            {...register("pathToTester")}
            type="text"
            placeholder="test/invariants/CryticTester.sol"
          />
        </div>

        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Echidna config filename"
            {...register("echidnaConfig")}
            type="text"
            placeholder="echidna.yaml"
          />
        </div>
      </div>

      <div className={FORM_STYLES.inputGroup}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Tester Contract Name"
            {...register("contract")}
            type="text"
            placeholder="CryticTester"
          />
        </div>

        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Corpus Dir"
            {...register("corpusDir")}
            type="text"
            placeholder="echidna"
          />
        </div>
      </div>

      <div className={FORM_STYLES.inputGroup}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Test Limit"
            {...register("testLimit")}
            type="text"
          />
        </div>

        <div className={FORM_STYLES.fieldContainer}>
          <AppSelect
            className={FORM_STYLES.input}
            label="Select Mode"
            {...register("testMode", { required: "Mode is required" })}
            options={TEST_MODE_OPTIONS}
          />
        </div>
      </div>

      <div className={FORM_STYLES.inputGroupSingle}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppSelect
            className={FORM_STYLES.input}
            label="Select Fork Mode"
            {...register("forkMode")}
            options={FORK_MODE_OPTIONS}
          />
        </div>
      </div>

      {forkMode && forkMode === "CUSTOM" && (
        <div className={FORM_STYLES.inputGroupSingle}>
          <div className={FORM_STYLES.fieldContainer}>
            <AppInput
              className={FORM_STYLES.input}
              label="RPC URL"
              {...register("rpcUrl")}
              type="text"
              defaultValue=""
            />
          </div>
        </div>
      )}

      {forkMode && forkMode !== "NONE" && (
        <div className={FORM_STYLES.inputGroupSingle}>
          <div className={FORM_STYLES.fieldContainer}>
            <AppInput
              className={FORM_STYLES.input}
              label="Fork Block"
              {...register("forkBlock")}
              type="text"
              defaultValue="LATEST"
            />
          </div>
        </div>
      )}

      {forkMode && forkMode !== "NONE" && (
        <div className={FORM_STYLES.inputGroupSingle}>
          <div className={FORM_STYLES.fieldContainer}>
            <AppSwitch
              label="Dynamic Block Replacement"
              enabled={watch("forkReplacement")}
              {...register("forkReplacement")}
              tooltip="This allows Recon to dynamically replace the fork block and timestamp in your tester. Requires the use of Recon specific tags."
            />
          </div>
        </div>
      )}
    </div>
  );
};

export const SubFormEchidnaAdvanced = () => {
  const { register } = useFormContext();

  return (
    <div className={FORM_STYLES.formContainer}>
      <div className={FORM_STYLES.inputGroup}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Corpus Re-use Job ID"
            {...register("targetCorpus")}
            type="text"
            placeholder="Optional: Job ID to reuse corpus from"
          />
        </div>
        <div className={FORM_STYLES.fieldContainer}>
          <PreInstallItem />
        </div>
      </div>
    </div>
  );
};

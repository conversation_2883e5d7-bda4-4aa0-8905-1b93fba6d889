import type { ReactNode } from "react";
import { Component, memo } from "react";
import { CopyBlock } from "react-code-blocks";

import { useTheme } from "next-themes";
import { dracula, paraisoLight } from "react-code-blocks";
import { THEME_OPTIONS } from "../services/ThemeProvider";

export const useCodeBlockTheme = () => {
  const { theme } = useTheme();
  const isLight = theme === THEME_OPTIONS.light;

  return isLight ? paraisoLight : dracula;
};

class ErrorBoundary extends Component<
  { children: ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, info: any) {
    console.error("AppCode error:", error, info);
  }

  render() {
    if (this.state.hasError) {
      return null;
    }
    return this.props.children;
  }
}

type AppCodeProps = {
  code: string;
  language: string;
  limit?: number;
  showLineNumbers?: boolean;
  maxHeight?: number;
  extraCss?: string;
};

export const AppCode = memo(
  ({
    code,
    language,
    limit = 1500,
    showLineNumbers = true,
    maxHeight,
    extraCss = "",
  }: AppCodeProps) => {
    const theme = useCodeBlockTheme();

    return (
      <ErrorBoundary>
        <div
          className={
            maxHeight ? `mb-3 h-[700px] ${extraCss}` : `mb-3 ${extraCss}`
          }
        >
          <CopyBlock
            text={code}
            language={language}
            theme={theme}
            customStyle={{
              overflowX: "auto",
              maxHeight: `${maxHeight}px`,
            }}
            showLineNumbers={showLineNumbers}
          />
        </div>
      </ErrorBoundary>
    );
  }
);

AppCode.displayName = "AppCode";

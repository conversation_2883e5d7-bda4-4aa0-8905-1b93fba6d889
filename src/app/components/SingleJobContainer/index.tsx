"use client";
import type { Fu<PERSON><PERSON>, FuzzingResults } from "@recon-fuzz/log-parser";
import { generateJobMD, processLogs } from "@recon-fuzz/log-parser";
import axios from "axios";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FaCheckCircle, FaFile, FaGithub } from "react-icons/fa";
import { ImLoop2 } from "react-icons/im";
import { IoMdDownload } from "react-icons/io";

import { downloadFile } from "@/app/(app)/tools/mdReportHelper";
import { AppButton } from "@/app/components/app-button";
import { AppSpinner } from "@/app/components/app-spinner";
import { Body3, H2 } from "@/app/components/app-typography";

import LogComponent from "../../(app)/tools/logs-parser/logs-parser";
import JobReport from "../JobReport/JobReport";
import { GlobalVmConfigComponent } from "../global-vm-config";
import { ShareJobButton } from "./share-job-button";
import { MainContentWrapper } from "@/app/(app)/components/main-content-wrapper";

interface BrokenProp {
  traces: string;
  brokenProperty: string;
}

export default function SingleJobContainer({
  isJobInfoLoading,
  shareInfo,
  jobId,
  reloadShares,
  jobData,
  isLoading,
}) {
  const pathname = usePathname();
  const router = useRouter();
  const [logs, setLogs] = useState<string>("");
  const [jobStats, setJobStats] = useState<FuzzingResults | null>(null);
  const [terminate, setTerminate] = useState(false);
  const [brokenProperties, setBrokenProperties] = useState<BrokenProp[]>([]);
  const [showJobReport, setShowJobReport] = useState<boolean>(false);
  const [rawlogs, setRawLogs] = useState<string>("");

  const stopJob = async () => {
    setTerminate(!terminate);

    try {
      await axios({
        method: "POST",
        url: `/api/jobs/stop`,
        data: {
          jobId,
        },
      });
    } catch (e) {
      alert(`Something went wrong: ${e.response.data.message}`);
      setTerminate(!terminate);
    }
  };

  const createMarkdown = () => {
    const md = generateJobMD(
      jobData?.fuzzer as Fuzzer,
      rawlogs,
      jobData?.label || `${jobData?.orgName}/${jobData?.repoName}`
    );
    return md;
  };

  useEffect(() => {
    const fetchLogs = async () => {
      const jobLogsRaw = await axios({
        method: "POST",
        url: `/api/fetchLogs`,
        data: {
          logsUrl: jobData?.logsUrl,
        },
      });
      if (jobLogsRaw.status === 200) {
        const data = processLogs(jobLogsRaw.data, jobData?.fuzzer);
        setRawLogs(jobLogsRaw.data);
        return {
          jobStats: data,
          logs: jobLogsRaw.data,
        };
      } else {
        return null;
      }
    };

    // This a general function to fetch the broken props ( for new runs from end sept 2024 )
    // It will also fetch the data from the logs
    // As a transition, if the broken props are not found, we will parse the logs as usual
    // Otherwise, we will reconciliate both data
    const fetchData = async () => {
      const brokenProps = jobData?.brokenProperties || [];
      const jobInfo = {
        duration: jobData?.testsDuration,
        coverage: jobData?.testsCoverage,
        failed: jobData?.testsFailed,
        passed: jobData?.testsPassed,
        numberOfTests: jobData?.numberOfTests,
        results: [],
        traces: [],
        brokenProperties: brokenProps,
      };
      const { logs, jobStats } = await fetchLogs();

      if (brokenProps.length > 0) {
        setBrokenProperties(brokenProps);
        setLogs(brokenProps.map((prop) => prop.traces).join("\n\n"));
        setJobStats({
          duration: jobInfo.duration,
          coverage: jobInfo.coverage,
          failed: jobInfo.failed,
          passed: jobInfo.passed,
          results: jobStats.results,
          numberOfTests: jobInfo.numberOfTests,
          traces: brokenProps.map((el) => el.traces),
          brokenProperties: brokenProps.map((el) => {
            return {
              brokenProperty: el.brokenProperty,
              sequence: el.traces,
            };
          }),
        });
      } else {
        setLogs(logs);
        setJobStats(jobStats);
      }
    };

    if (jobData?.fuzzer === "ECHIDNA" || jobData?.fuzzer === "MEDUSA") {
      fetchData();
    } else if (jobData?.logsUrl && jobData?.fuzzer) {
      fetchLogs().then((data) => {
        if (data) {
          setLogs(data.logs);
          setJobStats(data.jobStats);
        }
      });
    }
  }, [
    jobData?.brokenProperties,
    jobData?.fuzzer,
    jobData?.logsUrl,
    jobData?.testsCoverage,
    jobData?.testsDuration,
    jobData?.testsFailed,
    jobData?.testsPassed,
    jobData?.numberOfTests,
    jobId,
  ]);

  const rerunHandler = async () => {
    try {
      const res = await axios({
        method: "POST",
        url: `/api/jobs/rerun`,
        data: {
          jobId,
        },
      });
      if (res.status === 200) {
        alert("New job started");
        setTimeout(() => {
          router.push("/dashboard/jobs");
        }, 1000);
      }
    } catch (err) {
      alert(`Something went wrong: ${err.response.data.message}`);
    }
  };

  const isSharePage = pathname.includes("/shares");

  return (
    <MainContentWrapper>
      <div className="mx-auto p-6">
        {isLoading && (
          <div className="flex items-center justify-center py-6">
            <AppSpinner size={50} />
          </div>
        )}

        <div className="mb-4 space-y-3">
          {!isJobInfoLoading && jobData && jobData?.fuzzer && jobStats && (
            <div className="flex flex-wrap items-center gap-3">
              <AppButton
                variant="outline"
                onClick={() => setShowJobReport(!showJobReport)}
              >
                {showJobReport ? "Hide Job Report" : "Show Job Report"}
              </AppButton>

              {shareInfo ? (
                <Link href={`/shares/${shareInfo.id}/report`}>
                  <AppButton variant="primary" rightIcon={<FaFile />}>
                    See Report
                  </AppButton>
                </Link>
              ) : (
                <AppButton
                  variant="primary"
                  onClick={() =>
                    downloadFile(createMarkdown(), jobData.repoName)
                  }
                  rightIcon={<IoMdDownload />}
                >
                  Download Report
                </AppButton>
              )}

              {shareInfo && (
                <Link
                  href={`https://github.com/${jobData.orgName}/${
                    jobData.repoName
                  }${
                    jobData?.metadata?.commit
                      ? `/tree/${jobData.metadata.commit}`
                      : `/${jobData.ref}`
                  }`}
                  target="_blank"
                >
                  <AppButton variant="secondary" rightIcon={<FaGithub />}>
                    Go to Repo
                  </AppButton>
                </Link>
              )}

              <div className="ml-auto flex flex-wrap items-center gap-3">
                {!isJobInfoLoading && (
                  <ShareJobButton
                    jobId={jobId}
                    reloadShares={reloadShares}
                    shareInfo={shareInfo}
                  />
                )}

                {jobData?.corpusUrl && (
                  <Link target="_blank" href={jobData.corpusUrl}>
                    <AppButton variant="secondary" rightIcon={<IoMdDownload />}>
                      Download Corpus
                    </AppButton>
                  </Link>
                )}

                {!isSharePage && (
                  <AppButton
                    variant="outline"
                    onClick={rerunHandler}
                    rightIcon={<ImLoop2 />}
                  >
                    Rerun Job
                  </AppButton>
                )}

                {jobData?.status === "RUNNING" &&
                  !terminate &&
                  !pathname.includes("/share") && (
                    <AppButton
                      onClick={stopJob}
                      className="hover:bg-status-error/80 border-status-error bg-status-error text-accent-primary dark:text-white"
                    >
                      Terminate Job
                    </AppButton>
                  )}
              </div>
            </div>
          )}
        </div>

        {(jobData?.status === "QUEUED" ||
          jobData?.status === "RUNNING" ||
          jobData?.status === "STARTED" ||
          !jobData ||
          (jobData?.status !== "STOPPED" &&
            terminate &&
            !pathname.includes("/share"))) && (
          <div className="mb-4 flex flex-wrap items-center gap-3">
            {(jobData?.status === "QUEUED" ||
              jobData?.status === "RUNNING" ||
              jobData?.status === "STARTED") && (
              <div className="flex items-center gap-2 rounded-lg border border-stroke-neutral-decorative bg-back-neutral-tertiary px-3 py-2">
                <Body3 className="text-sm font-semibold text-accent-primary">
                  Status: {jobData?.status}
                </Body3>
                <Body3 color="secondary" className="text-xs">
                  Please check back soon for results.
                </Body3>
              </div>
            )}

            {jobData?.status !== "STOPPED" &&
              terminate &&
              !pathname.includes("/share") && (
                <div className="bg-status-success/10 flex items-center gap-2 rounded-lg border border-status-success px-3 py-2">
                  <FaCheckCircle className="size-4 text-status-success" />
                  <Body3 color="primary" className="text-xs">
                    Job will be terminated momentarily
                  </Body3>
                </div>
              )}
          </div>
        )}

        {!!jobData?.fuzzer && !!jobStats && (
          <div className="mb-4">
            <GlobalVmConfigComponent tool={jobData.fuzzer} className="mb-6" />
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
              <LogComponent
                fuzzer={jobData?.fuzzer}
                logs={logs}
                jobStatsForced={jobStats}
              />
              {!!showJobReport && (
                <JobReport
                  fuzzer={jobData.fuzzer}
                  jobStats={jobStats}
                  showBrokenProp={false}
                />
              )}
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          {logs && (
            <div className="rounded-lg border border-stroke-neutral-decorative bg-back-neutral-tertiary p-4">
              <div className="mb-3 flex items-center justify-between">
                <H2 className="text-lg text-accent-primary">
                  {brokenProperties.length > 0 ? "Compressed Logs" : "Logs"}
                </H2>
                <div className="flex items-center gap-2">
                  {jobData?.logsUrl && (
                    <Link
                      className="text-sm text-accent-primary underline hover:text-accent-secondary"
                      target="_blank"
                      href={jobData?.logsUrl}
                    >
                      Full Logs
                    </Link>
                  )}
                </div>
              </div>

              <div className="rounded-lg border border-stroke-neutral-decorative bg-back-neutral-primary">
                <textarea
                  name="logs"
                  id="logs"
                  className="h-[500px] w-full resize-none overflow-y-auto bg-back-neutral-primary p-3 font-mono text-sm text-fore-neutral-primary focus:outline-none"
                  value={logs}
                  readOnly
                />
              </div>
            </div>
          )}

          {jobData?.coverageUrl &&
            logs &&
            (jobData?.fuzzer === "ECHIDNA" || jobData?.fuzzer === "MEDUSA") && (
              <div className="rounded-lg border border-stroke-neutral-decorative bg-back-neutral-tertiary p-4">
                <div className="mb-3 flex items-center justify-between">
                  <H2 className="text-lg text-accent-primary">
                    Coverage Report
                  </H2>
                  <div className="flex items-center gap-2">
                    {jobData?.coverageUrl && (
                      <Link
                        className="text-sm text-accent-primary underline hover:text-accent-secondary"
                        target="_blank"
                        href={jobData?.coverageUrl}
                      >
                        Open in New Tab
                      </Link>
                    )}
                  </div>
                </div>

                <div className="overflow-hidden rounded-lg border border-stroke-neutral-decorative">
                  <iframe
                    src={jobData?.coverageUrl}
                    title="coverage"
                    className="h-[500px] w-full"
                  />
                </div>
              </div>
            )}
        </div>
      </div>
    </MainContentWrapper>
  );
}

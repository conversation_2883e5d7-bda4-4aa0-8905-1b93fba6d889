"use client";

import axios from "axios";
import { useState } from "react";
import { <PERSON>a<PERSON>heck, FaLink } from "react-icons/fa";
import { toast } from "sonner";

import { AppButton } from "@/app/components/app-button";
import { AppSpinner } from "@/app/components/app-spinner";

export const ShareJobButton = ({ jobId, reloadShares, shareInfo }) => {
  const [loading, setLoading] = useState(false);

  async function shareTheJob() {
    if (!jobId) {
      return;
    }

    setLoading(true);

    try {
      const response = await axios({
        method: "POST",
        url: "/api/shares",
        data: {
          jobId,
        },
      });

      // Auto-copy the share link to clipboard
      const shareId = response.data.data.id;
      const shareUrl = `${window.location.origin}/shares/${shareId}`;

      await navigator.clipboard.writeText(shareUrl);
      toast.success("Share link copied to clipboard!");
    } catch (e) {
      console.log(e);
      toast.error(
        `Something went wrong: ${
          e.response?.data?.message || "Failed to create share"
        }`
      );
    }

    reloadShares();
    setLoading(false);
  }

  // TODO: if shareInfo
  // Then turn it into clipboard copy tool

  const [copied, setCopied] = useState(false);

  async function handleCopy() {
    try {
      await navigator.clipboard.writeText(
        `${window.location.origin}/shares/${shareInfo.id}`
      );
      setCopied(true);
      toast.success("Share link copied to clipboard!");
      setTimeout(() => setCopied(false), 2000); // Reset after 2 seconds
    } catch (err) {
      console.error("Failed to copy: ", err);
      toast.error("Failed to copy link to clipboard");
    }
  }

  if (shareInfo) {
    return (
      <AppButton
        onClick={handleCopy}
        variant="primary"
        rightIcon={copied ? <FaCheck /> : <FaLink />}
      >
        Copy Share URL
      </AppButton>
    );
  }

  return (
    <AppButton disabled={loading} onClick={shareTheJob}>
      {loading ? (
        <AppSpinner />
      ) : (
        <>
          <span>Share Job Results</span>
          <FaLink />
        </>
      )}
    </AppButton>
  );
};

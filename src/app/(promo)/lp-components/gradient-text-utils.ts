import type { CSSProperties } from "react";

/**
 * Utility for creating gradient text styles
 * Replaces the duplicated inline styles and custom CSS classes
 */

export const GRADIENT_TEXT_STYLES = {
  primary: {
    background:
      "linear-gradient(277.04deg, var(--gradient-primary-start) 16.04%, var(--gradient-primary-end) 51.63%)",
    WebkitBackgroundClip: "text",
    backgroundClip: "text",
    WebkitTextFillColor: "transparent",
    display: "inline-block",
  } as CSSProperties,
  
  main: {
    background:
      "linear-gradient(277.21deg, var(--gradient-primary-start) -13.33%, var(--gradient-primary-end) 51.57%)",
    WebkitBackgroundClip: "text",
    backgroundClip: "text",
    WebkitTextFillColor: "transparent",
    display: "inline-block",
  } as CSSProperties,
} as const;

/**
 * Creates gradient text style with optional font size override
 */
export const createGradientTextStyle = (
  variant: keyof typeof GRADIENT_TEXT_STYLES = "primary",
  fontSize?: string
): CSSProperties => ({
  ...GRADIENT_TEXT_STYLES[variant],
  ...(fontSize && { fontSize }),
});

/**
 * CSS class names for gradient text that can be used with Tailwind
 */
export const GRADIENT_TEXT_CLASSES = {
  base: "bg-clip-text text-transparent bg-gradient-to-r",
  primary: "from-gradient-primary-start to-gradient-primary-end",
} as const;

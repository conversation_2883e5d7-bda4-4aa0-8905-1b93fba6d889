export interface VmParsingData {
  roll: boolean;
  time: boolean;
  prank: boolean;
}

// Legacy interface - kept for backward compatibility
// New implementations should use GlobalVmConfig from vm-config.ts
export interface LegacyVmParsingData extends VmParsingData {}

export interface TracesShower {
  id: number;
  show: boolean;
}

export interface BrokenPropShow {
  id: number;
  show: boolean;
}

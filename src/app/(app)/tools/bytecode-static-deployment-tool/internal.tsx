"use client";
import { use<PERSON>em<PERSON>, useState } from "react";
import Link from "next/link";
import { FiArrowLeft } from "react-icons/fi";

import { AppTextarea } from "@/app/components/app-textarea";
import { AppCode } from "@/app/components/app-code";
import { Body2, H1, H3 } from "@/app/components/app-typography";
import { GradientWrapper } from "@/app/components/gradient-wrapper";
import { <PERSON>ppHeader } from "@/app/(app)/components/app-header";

/**
 * Generates the initialization code for a Solidity Smart Contract
 * following the pattern:
 *
 * PUSH1 <size>     // Size of runtime code
 * PUSH1 <offset>   // Offset where runtime code begins in the full bytecode
 * PUSH1 <memory offset> // Destination offset in memory (0)
 * CODECOPY         // Copy runtime code to memory
 *
 * PUSH1 <size>     // Size for return
 * PUSH1 <memory offset> // Offset for return
 * RETURN           // Return bytes from memory as the runtime code
 *
 * @param runtimeBytecode - The runtime bytecode of the contract (without the init code)
 * @returns The full initialization code (hexadecimal string)
 */
function generateInitCode(runtimeBytecode: string): string {
  // Remove '0x' prefix if present
  const cleanBytecode = runtimeBytecode.startsWith("0x")
    ? runtimeBytecode.slice(2)
    : runtimeBytecode;

  // Calculate the size of the runtime bytecode (in bytes)
  const runtimeSize = cleanBytecode.length / 2; // 2 hex chars = 1 byte

  // Calculate the offset where runtime code begins
  // In this simple case, it's just the size of the init code
  // Our init code will be ~37 bytes (74 hex chars) in this implementation
  const runtimeOffset = 74;

  // Memory offset (typically 0)
  const memoryOffset = 0;

  // Convert decimal values to hex
  // Size needs 64 chars (32 bytes) for PUSH32
  // Others need 2 chars (1 byte) for PUSH1
  const runtimeSizeHex = runtimeSize.toString(16).padStart(64, "0");
  const runtimeOffsetHex = runtimeOffset.toString(16).padStart(2, "0");
  const memoryOffsetHex = memoryOffset.toString(16).padStart(2, "0");

  // PUSH32 <size>
  const pushSize = `7F${runtimeSizeHex}`;

  // PUSH1 <offset>
  const pushOffset = `60${runtimeOffsetHex}`;

  // PUSH1 <memory offset>
  const pushMemOffset = `60${memoryOffsetHex}`;

  // CODECOPY opcode is 0x39
  const codecopy = "39";

  // PUSH32 <size> (again for RETURN)
  const pushSizeForReturn = `7F${runtimeSizeHex}`;

  // PUSH1 <memory offset> (again for RETURN)
  const pushMemOffsetForReturn = `60${memoryOffsetHex}`;

  // RETURN opcode is 0xf3
  const returnOp = "f3";

  // Combine all parts
  const initCode =
    pushSize +
    pushOffset +
    pushMemOffset +
    codecopy +
    pushSizeForReturn +
    pushMemOffsetForReturn +
    returnOp;

  // Return the complete bytecode (initCode + runtimeBytecode)
  return "0x" + initCode;
}

export default function BytecodeStaticDeploymentToolInternal() {
  const [bytecode, setBytecode] = useState("");

  const initCode = useMemo(() => {
    return generateInitCode(bytecode);
  }, [bytecode]);

  return (
    <div className="main-container w-full overflow-x-hidden">
      <div className="relative z-10 min-h-screen">
        <AppHeader skipUser />

        <GradientWrapper className="flex min-h-[calc(100vh-80px)] items-center justify-center py-8">
          <div className="w-full max-w-4xl rounded-[20px] bg-back-neutral-tertiary px-[48px] py-[40px]">
            <div className="mb-8 text-left">
              <Link
                href="/tools"
                className="mb-6 inline-flex items-center text-fore-neutral-primary transition-colors duration-200 hover:text-fore-neutral-secondary"
              >
                <FiArrowLeft className="size-10" />
              </Link>

              <H1 className="mb-6 text-accent-primary">
                Bytecode Static Deployment Tool
              </H1>

              <Body2 className="mb-2">
                Given some bytecode, this tool generates the initCode necessary
                to make it work
              </Body2>
              <Body2 className="mb-6">
                NOTE: The tool assumes the bytecode has no constructor!
              </Body2>
            </div>

            <div className="mb-8">
              <AppTextarea
                className="mb-6"
                label="Contract Bytecode"
                value={bytecode}
                onChange={(e) => setBytecode(e.target.value)}
                placeholder="Enter your contract bytecode here..."
              />
            </div>

            <div className="space-y-6">
              <div>
                <H3 className="mb-4 text-fore-neutral-primary">Encoded</H3>
                <AppCode
                  showLineNumbers={false}
                  code={initCode}
                  language="python"
                />
              </div>

              <div>
                <H3 className="mb-4 text-fore-neutral-primary">Both</H3>
                <AppCode
                  showLineNumbers={false}
                  code={`${initCode}${bytecode}`}
                  language="python"
                />
              </div>
            </div>
          </div>
        </GradientWrapper>
      </div>
    </div>
  );
}

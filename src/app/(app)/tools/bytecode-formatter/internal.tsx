"use client";
import { useEffect, useState } from "react";
import { AppTextarea } from "@/app/components/app-textarea";
import { formatHexData } from "@/lib/utils";
import { AppCode } from "@/app/components/app-code";
import { AppButton } from "@/app/components/app-button";
import { Body2, H1, H3 } from "@/app/components/app-typography";
import { AppHeader } from "@/app/(app)/components/app-header";
import { GradientWrapper } from "@/app/components/gradient-wrapper";
import Link from "next/link";
import { FiArrowLeft } from "react-icons/fi";

export default function BytecodeFormatterInternal() {
  const [bytecode, setBytecode] = useState("");
  const [mode, setMode] = useState<"words" | "calldata">("calldata");

  const [comparisonResult, setComparisonResult] = useState<string | null>(null);

  useEffect(() => {
    setComparisonResult(formatHexData(bytecode, mode));
  }, [bytecode, mode]);

  return (
    <div className="main-container w-full overflow-x-hidden">
      <div className="relative z-10 min-h-screen">
        <AppHeader skipUser />
        <GradientWrapper className="min-h-[calc(100vh-80px)] py-8">
          <div className="mx-auto w-full max-w-6xl rounded-[20px] bg-back-neutral-tertiary px-[48px] py-[40px]">
            <div className="mb-8 text-left">
              <Link
                href="/tools"
                className="mb-6 inline-flex items-center text-fore-neutral-primary transition-colors duration-200 hover:text-fore-neutral-secondary"
              >
                <FiArrowLeft className="size-10" />
              </Link>

              <H1 className="mb-6 text-accent-primary">Bytecode Formatter</H1>

              <Body2 className="mb-2">
                This tool formats bytecode and calldata into 32 bytes per row
              </Body2>
              <Body2 className="mb-6">
                Calldata has it's selectors (first 4 bytes) separate as well
              </Body2>

              <div className="mb-6 flex items-center gap-4">
                <Body2 className="font-medium text-fore-neutral-primary">
                  Format as:
                </Body2>
                <AppButton
                  onClick={() => {
                    if (mode == "words") {
                      setMode("calldata");
                    } else {
                      setMode("words");
                    }
                  }}
                  variant="secondary"
                  size="sm"
                >
                  {mode === "words" ? "Switch to Calldata" : "Switch to Words"}
                </AppButton>
              </div>
            </div>

            <div className="mb-8">
              <AppTextarea
                className="mb-4"
                label={`${mode.charAt(0).toUpperCase() + mode.slice(1)} Input`}
                value={bytecode}
                onChange={(e) => setBytecode(e.target.value)}
                placeholder={`Enter your ${mode} here...`}
              />
            </div>

            {comparisonResult && (
              <div>
                <H3 className="mb-4 text-fore-neutral-primary">
                  Formatted Output
                </H3>
                <AppCode
                  showLineNumbers={false}
                  code={comparisonResult}
                  language="python"
                />
              </div>
            )}
          </div>
        </GradientWrapper>
      </div>
    </div>
  );
}

"use client";
import { useState } from "react";
import Link from "next/link";
import { FiArrowLeft } from "react-icons/fi";
import init, { contractInfo } from "evmole/no_tla";
import { ethers } from "ethers";

import { AppInput } from "@/app/components/app-input";
import { AppButton } from "@/app/components/app-button";
import { AppCode } from "@/app/components/app-code";
import { AppTextarea } from "@/app/components/app-textarea";
import { Body2, H1, H3 } from "@/app/components/app-typography";
import { GradientWrapper } from "@/app/components/gradient-wrapper";
import { AppHeader } from "@/app/(app)/components/app-header";
import { lookupFunctionSignature } from "../../../../utils/contractInterfaceUtils";

export default function BytecodeToInterfaceInternal() {
  const [interfaceName, setInterfaceName] = useState("IContract");
  const [rpc, setRpc] = useState("https://eth.llamarpc.com/");
  const [address, setAddress] = useState(
    "******************************************"
  );
  const [bytecode, setBytecode] = useState("");
  const [generatedInterface, setGeneratedInterface] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingBytecode, setIsFetchingBytecode] = useState(false);
  const [fetchError, setFetchError] = useState("");
  const [generateError, setGenerateError] = useState("");

  const fetchContractBytecode = async () => {
    try {
      setIsFetchingBytecode(true);
      setFetchError("");

      if (!rpc || !address) {
        setFetchError("Please provide both RPC URL and contract address");
        setIsFetchingBytecode(false);
        return;
      }

      // Create ethers provider with the given RPC URL
      const provider = new ethers.JsonRpcProvider(rpc);

      // Fetch bytecode using ethers
      const fetchedBytecode = await provider.getCode(address);

      if (fetchedBytecode === "0x" || fetchedBytecode === "") {
        setFetchError(
          "No bytecode found at this address. Make sure the address is correct and the contract is deployed."
        );
        setIsFetchingBytecode(false);
        return;
      }

      setBytecode(fetchedBytecode);
      setIsFetchingBytecode(false);
    } catch (err) {
      setFetchError(`Failed to fetch bytecode: ${err.message}`);
      setIsFetchingBytecode(false);
    }
  };

  const generateInterface = async () => {
    try {
      setGeneratedInterface("");
      setIsLoading(true);
      setGenerateError("");

      if (!bytecode || bytecode === "0x") {
        setGenerateError("Please provide contract bytecode first");
        setIsLoading(false);
        return;
      }

      await init();
      // Parse bytecode using evmole
      const info = contractInfo(bytecode, {
        selectors: true,
        arguments: true,
        stateMutability: true,
      });

      // Generate Solidity interface
      let interfaceCode = `// SPDX-License-Identifier: MIT\npragma solidity ^0.8.0;\n\ninterface ${interfaceName} {\n`;

      // Map to track function signatures we've already processed
      const processedFunctions = new Map();

      // Add functions to interface
      if (info.functions && info.functions.length > 0) {
        for (let i = 0; i < info.functions.length; i++) {
          const func = info.functions[i];
          const selector = func.selector;

          // Skip if we've already processed this selector
          if (processedFunctions.has(selector)) continue;
          processedFunctions.set(selector, true);

          const argTypes = func.arguments
            ? func.arguments.split(",").filter(Boolean)
            : [];
          const stateMutability =
            func.stateMutability === "payable" ? " payable" : "";

          // Try to lookup function name from public signature databases
          let functionName = null;
          let functionMatched = false;

          const signature = await lookupFunctionSignature(
            selector,
            argTypes,
            i
          );
          console.log("Signature:", signature);
          if (signature) {
            functionName = signature.name.split("(")[0]; // Get name without parameters
            if (signature.defs) {
              interfaceCode += `\n    ${signature.defs}\n`;
            }
            functionMatched = true;
          }

          // Default to selector if no name found
          if (!functionName) {
            functionName = `func_${selector}`;
          }

          // Create function signature
          let argsList = "";
          if (functionMatched) {
            argsList = signature.paramTypes
              .map((paramType) => {
                const isArray = paramType.includes("[");
                const isStruct = paramType.startsWith("S" + i + "_");
                const isBytes =
                  paramType.startsWith("bytes") &&
                  !paramType.includes("bytes32");
                const isString = paramType.startsWith("string");
                const needsMemory = isArray || isStruct || isBytes || isString;
                return needsMemory
                  ? paramType.replace(" arg", " memory arg")
                  : paramType;
              })
              .join(", ");
          } else {
            if (argTypes.length > 0) {
              argsList = argTypes
                .map((arg, i) => {
                  const isArray = arg.includes("[");
                  const isBytes =
                    arg.startsWith("bytes") && !arg.includes("bytes32");
                  const isString = arg.startsWith("string");
                  const needsMemory = isArray || isBytes || isString;
                  return `${arg}${needsMemory ? " memory" : ""} arg${i}`;
                })
                .join(", ");
            }
          }

          // For unmatched functions, add as commented out
          const prefix = functionMatched ? "" : "// ";

          interfaceCode += `    ${prefix}function ${functionName}(${argsList}) external${stateMutability};\n`;
        }
      } else {
        setGenerateError("No functions found in the bytecode");
        setIsLoading(false);
        return;
      }

      interfaceCode += "}";

      setGeneratedInterface(interfaceCode);
      setIsLoading(false);
    } catch (err) {
      setGenerateError(`Failed to generate interface: ${err.message}`);
      setIsLoading(false);
    }
  };

  return (
    <div className="main-container w-full overflow-x-hidden">
      <div className="relative z-10 min-h-screen">
        <AppHeader skipUser />

        <GradientWrapper className="flex min-h-[calc(100vh-80px)] items-center justify-center py-8">
          <div className="w-full max-w-6xl rounded-[20px] bg-back-neutral-tertiary px-[48px] py-[40px]">
            <div className="mb-8 text-left">
              <Link
                href="/tools"
                className="mb-6 inline-flex items-center text-fore-neutral-primary transition-colors duration-200 hover:text-fore-neutral-secondary"
              >
                <FiArrowLeft className="size-10" />
              </Link>

              <H1 className="mb-6 text-accent-primary">
                Bytecode to Interface
              </H1>

              <Body2 className="mb-2">
                This tool converts contract bytecode to a Solidity interface by
                analyzing function selectors
              </Body2>
              <Body2 className="mb-6">
                Enter RPC URL and address to fetch bytecode, or paste bytecode
                directly
              </Body2>
            </div>

            <div className="mb-8">
              <H3 className="mb-4 text-fore-neutral-primary">
                Step 1: Get Contract Bytecode
              </H3>
              <div className="mb-6">
                <AppInput
                  className="mb-4"
                  label="RPC URL (e.g. https://mainnet.infura.io/v3/YOUR-API-KEY)"
                  value={rpc}
                  onChange={(e) => setRpc(e.target.value)}
                  type="text"
                  placeholder="https://..."
                />

                <AppInput
                  className="mb-4"
                  label="Contract Address"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  type="text"
                  placeholder="0x..."
                />

                <AppButton
                  className="mt-2"
                  onClick={fetchContractBytecode}
                  disabled={isFetchingBytecode || !rpc || !address}
                >
                  {isFetchingBytecode ? "Fetching..." : "Fetch Bytecode"}
                </AppButton>

                {fetchError && (
                  <div className="bg-status-error/10 mt-2 rounded p-3 text-status-error">
                    {fetchError}
                  </div>
                )}
              </div>
              <H3 className="mb-4 text-fore-neutral-primary">
                or simply paste bytecode below
              </H3>
              <AppTextarea
                className="mb-6"
                label="Contract Bytecode"
                value={bytecode}
                onChange={(e) => setBytecode(e.target.value)}
                placeholder="0x..."
              />
            </div>

            <div className="mb-8">
              <H3 className="mb-4 text-fore-neutral-primary">
                Step 2: Configure Interface
              </H3>
              <AppInput
                className="mb-6"
                label="Interface Name"
                value={interfaceName}
                onChange={(e) => setInterfaceName(e.target.value)}
                type="text"
                placeholder="IContract"
              />
            </div>

            <div className="mb-8">
              <H3 className="mb-4 text-fore-neutral-primary">
                Step 3: Generate Interface
              </H3>
              <AppButton
                className="mb-6"
                onClick={generateInterface}
                disabled={isLoading}
              >
                {isLoading ? "Generating..." : "Generate Interface"}
              </AppButton>
            </div>

            {generateError && (
              <div className="bg-status-error/10 mb-6 rounded p-3 text-status-error">
                {generateError}
              </div>
            )}

            {generatedInterface && (
              <AppCode code={generatedInterface} language="solidity" />
            )}
          </div>
        </GradientWrapper>
      </div>
    </div>
  );
}

import axios from "axios";
import Link from "next/link";
import { FormProvider, useForm } from "react-hook-form";

import { useGetCampaigns } from "@/app/services/campaigns.hook";
import { useGetRecipes } from "@/app/services/recipes.hook";

import { AppInputDropdown } from "@/app/components/app-input-dropdown";
import { AppButton } from "../../../components/app-button";
import { AppInput } from "../../../components/app-input";
import { AppSpinner } from "../../../components/app-spinner";
import { Body3, Heading } from "../../../components/app-typography";
import { FORM_STYLES } from "../../../components/create-job-form/constants";

type GitHubLinkInputProps = {
  title?: string;
  submitLabel?: string;
  hidePresets?: boolean;
};

// TODO
/**
 * displayName, orgNames, repoNames, branchNames, recipeIds
 *
 */
export type CampaignCreationFormValues = {
  // UX
  githubURL: string;

  // Data
  displayName: string;
  orgName: string;
  repoName: string;
  branchName: string;
  recipeId: string;
};

// TODO: On Commit, On PR + Commit (TODO: Issue with double firing of events)
// const radioOptions = [
//   {
//     label: "Medusa",
//     value: "MEDUSA",
//   },
//   {
//     label: "Echidna",
//     value: "ECHIDNA",
//   },
// ];

export function CreateCampaign({
  title = "Create Campaign",
  submitLabel = "Create Campaign",
}: GitHubLinkInputProps) {
  const {
    register,
    handleSubmit,
    setValue,
    setError,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<CampaignCreationFormValues>();

  const { refetch: refetchCampaigns } = useGetCampaigns();
  const { data: recipes, isLoading: isLoadingRecipes } = useGetRecipes();

  const onSubmit = async ({
    displayName,
    orgName,
    repoName,
    branchName,
    recipeId,
  }: CampaignCreationFormValues) => {
    // TODO: We need to pass them as Array of Values
    if (recipes.filter((el) => el.id === recipeId).length === 0) {
      alert("Invalid Recipe ID");
      return;
    }
    try {
      await axios({
        method: "POST",
        url: `/api/campaigns`,
        data: {
          displayName, // Can be empty string
          orgNames: [orgName],
          repoNames: [repoName],
          branchNames: [branchName],
          recipeIds: [recipeId],
        },
      });
      refetchCampaigns();
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  const parseURI = (inputValue) => {
    const success = /^(https?:\/\/)?(www\.)?github\.com\/.+\/.+(\.git)?$/.test(
      inputValue
    );

    if (!success) {
      setError("githubURL", {
        message: "Invalid GitHub URL",
      });
      return;
    }

    const ghLink = inputValue.endsWith("/")
      ? inputValue.slice(0, -1)
      : inputValue;
    const uriParts = ghLink
      .replace("https://", "")
      .replace("http://", "")
      .split("/");

    if (uriParts.length >= 3) {
      const orgName = uriParts[1];
      const repoName = uriParts[2];
      let branchName = "main"; // Default branch

      // Check if the URL specifies a branch
      if (uriParts.length > 5 && uriParts[3] === "tree") {
        // The branch name can include slashes, so we join the remaining parts
        branchName = uriParts.slice(4).join("/");
      } else if (uriParts.length === 5 && uriParts[3] === "tree") {
        // Handle the case where there's no slash in the branch name
        branchName = uriParts[4];
      }

      // Set the values to the form
      setValue("orgName", orgName);
      setValue("repoName", repoName);
      setValue("branchName", branchName);
    }
  };

  const githubUrlRegister = register("githubURL");
  return (
    <FormProvider
      {...({
        register,
        handleSubmit,
        setValue,
        setError,
        watch,
        errors,
        isSubmitting,
      } as any)}
    >
      <form
        onSubmit={handleSubmit(onSubmit)}
        className={FORM_STYLES.formContainer}
      >
        <Heading color="primary" className="mb-6">
          {title}
        </Heading>

        {recipes && recipes.length > 0 && !isLoadingRecipes ? (
          <div className="space-y-6">
            <div className={FORM_STYLES.inputGroupSingle}>
              <div className={FORM_STYLES.fieldContainer}>
                <AppInput
                  className={FORM_STYLES.input}
                  label="Campaign Display Name"
                  {...register("displayName")}
                  type="text"
                  placeholder="Enter a descriptive name for your campaign"
                />
              </div>
            </div>

            <div className={FORM_STYLES.inputGroupSingle}>
              <div className={FORM_STYLES.fieldContainer}>
                <AppInput
                  {...githubUrlRegister}
                  className={FORM_STYLES.input}
                  label="GitHub Repository URL"
                  onChange={(e) => {
                    githubUrlRegister.onChange(e);
                    parseURI(e.target.value);
                  }}
                  type="text"
                  placeholder="https://github.com/owner/repository"
                  error={errors.githubURL?.message}
                />
              </div>
            </div>

            <div className={FORM_STYLES.divider} />

            <div>
              <Body3 color="secondary" className="mb-4">
                Or specify the repository details manually
              </Body3>

              <div className={FORM_STYLES.inputGroup}>
                <div className={FORM_STYLES.fieldContainer}>
                  <AppInput
                    className={FORM_STYLES.input}
                    label="Organization"
                    {...register("orgName")}
                    type="text"
                    placeholder="e.g., ethereum"
                  />
                </div>

                <div className={FORM_STYLES.fieldContainer}>
                  <AppInput
                    className={FORM_STYLES.input}
                    {...register("repoName")}
                    type="text"
                    label="Repository"
                    placeholder="e.g., solidity"
                  />
                </div>
              </div>

              <div className={FORM_STYLES.inputGroup}>
                <div className={FORM_STYLES.fieldContainer}>
                  <AppInput
                    className={FORM_STYLES.input}
                    {...register("branchName")}
                    type="text"
                    label="Branch"
                    placeholder="e.g., main, develop"
                  />
                </div>

                <div className={FORM_STYLES.fieldContainer}>
                  <AppInputDropdown
                    className={FORM_STYLES.input}
                    {...register("recipeId")}
                    type="text"
                    label="Recipe"
                    placeholder="Search by name, ID, or repository..."
                    dropdownItems={recipes.map((rec) => ({
                      id: rec.id,
                      label: `${rec.displayName}`,
                      fields: rec,
                    }))}
                    onItemSelect={(id) => setValue("recipeId", id as string)}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-start pt-4">
              <AppButton
                type="submit"
                disabled={isSubmitting}
                variant="primary"
                size="default"
              >
                {isSubmitting ? <AppSpinner /> : submitLabel}
              </AppButton>
            </div>
          </div>
        ) : !isLoadingRecipes && (!recipes || recipes.length === 0) ? (
          <div className="text-fore-neutral-primary">
            <p>No recipes found.</p>
            <Link href="/dashboard/recipes/" className="cursor-pointer">
              <AppButton variant="primary">
                Please create a recipe first
              </AppButton>
            </Link>
          </div>
        ) : (
          <p className="text-fore-neutral-primary">Loading ...</p>
        )}
      </form>
    </FormProvider>
  );
}

"use client";

import { App<PERSON><PERSON>on } from "@/app/components/app-button";
import { AppInput } from "@/app/components/app-input";
import { Body3 } from "@/app/components/app-typography";
import { FORM_STYLES, type EditFormType } from "./constants";

interface SetupEditFormProps {
  editForm: EditFormType;
  onInputChange: (field: string, value: string) => void;
  onPrepareContractChange: (index: number, field: string, value: string) => void;
  onValidate: () => void;
  onCancel: () => void;
}

export function SetupEditForm({
  editForm,
  onInputChange,
  onPrepareContractChange,
  onValidate,
  onCancel,
}: SetupEditFormProps) {
  return (
    <div className="space-y-6">
      <h3 className={FORM_STYLES.sectionTitle}>
        Edit Governance Fuzzing Setup
      </h3>
      
      <div className={FORM_STYLES.inputGroup}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Address"
            type="text"
            value={editForm.address}
            onChange={(e) => onInputChange("address", e.target.value)}
          />
        </div>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Chain Id"
            type="text"
            value={editForm.chainId}
            onChange={(e) => onInputChange("chainId", e.target.value)}
          />
        </div>
      </div>

      <div className="space-y-4">
        <Body3 color="primary" className="font-semibold">
          Prepare Contracts
        </Body3>
        <div className="space-y-3">
          {editForm.prepareContracts.map((contract, index) => (
            <AppInput
              key={`contract-${contract.target}-${index}`}
              className={FORM_STYLES.input}
              type="text"
              value={contract.target}
              placeholder="Contract address"
              onChange={(e) =>
                onPrepareContractChange(index, "target", e.target.value)
              }
            />
          ))}
        </div>
      </div>

      <div className={FORM_STYLES.inputGroupSingle}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Event Definition"
            type="text"
            value={editForm.eventDefinition}
            onChange={(e) => onInputChange("eventDefinition", e.target.value)}
          />
        </div>
      </div>

      <div className={FORM_STYLES.inputGroupSingle}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Topic"
            type="text"
            value={editForm.topic}
            disabled
          />
        </div>
      </div>

      <div className="flex gap-4">
        <AppButton type="button" onClick={onValidate}>
          Validate Edit
        </AppButton>
        <AppButton type="button" variant="secondary" onClick={onCancel}>
          Cancel Edit
        </AppButton>
      </div>
    </div>
  );
}

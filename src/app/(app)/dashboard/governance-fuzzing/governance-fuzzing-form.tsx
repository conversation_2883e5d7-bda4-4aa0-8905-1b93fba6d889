"use client";

import { useFormContext } from "react-hook-form";
import { AppInput } from "@/app/components/app-input";
import { AppInputDropdown } from "@/app/components/app-input-dropdown";
import { Body3 } from "@/app/components/app-typography";
import { chains } from "@/lib/utils";
import { EventDefinitionSection } from "./event-definition-section";
import { FORM_STYLES, type GitHubLinkFormValues } from "./constants";
import type { Recipe } from "@/app/services/recipes.hook";

interface GovernanceFuzzingFormProps {
  recipes: Recipe[] | undefined;
  isLoading: boolean;
  contractAddress: string;
  chainId: number;
  recipeId: string;
  showEventDefinition: boolean;
  onShowEventDefinition: (show: boolean) => void;
  topic: string;
  eventDefinition: string;
}

export function GovernanceFuzzingForm({
  recipes,
  isLoading,
  contractAddress,
  chainId,
  recipeId,
  showEventDefinition,
  onShowEventDefinition,
  topic,
  eventDefinition,
}: GovernanceFuzzingFormProps) {
  const { register, setValue } = useFormContext<GitHubLinkFormValues>();

  return (
    <div className={FORM_STYLES.formContainer}>
      <div className="space-y-6">
        <div className={FORM_STYLES.inputGroup}>
          <div className={FORM_STYLES.fieldContainer}>
            <AppInput
              className={FORM_STYLES.input}
              label="Contract Address"
              {...register("contractAddress", { required: true })}
              type="text"
            />
          </div>
          <div className={FORM_STYLES.fieldContainer}>
            <AppInputDropdown
              className={FORM_STYLES.input}
              label="Chain"
              {...register("chainId")}
              type="text"
              dropdownItems={chains.map((chain) => ({
                id: chain.id.toString(),
                label: `${chain.id} - ${chain.name}`,
                fields: chain,
              }))}
              onItemSelect={(id) => setValue("chainId", Number(id))}
            />
          </div>
        </div>

        {recipes && recipes.length > 0 && !isLoading ? (
          <div className={FORM_STYLES.inputGroupSingle}>
            <div className={FORM_STYLES.fieldContainer}>
              <AppInputDropdown
                className={FORM_STYLES.input}
                {...register("recipeId", { required: true })}
                type="text"
                label="Recipe"
                placeholder="Search by Name, Id, repo ..."
                dropdownItems={recipes.map((rec) => ({
                  id: rec.id,
                  label: `${rec.displayName}`,
                  fields: rec,
                }))}
                onItemSelect={(id) => setValue("recipeId", id as string)}
              />
            </div>
          </div>
        ) : isLoading ? (
          <Body3 color="primary">Loading recipes ...</Body3>
        ) : (
          <Body3 color="primary">No recipes found</Body3>
        )}

        {contractAddress && chainId && recipeId && (
          <EventDefinitionSection
            showEventDefinition={showEventDefinition}
            onShowEventDefinition={onShowEventDefinition}
            topic={topic}
            eventDefinition={eventDefinition}
          />
        )}
      </div>
    </div>
  );
}

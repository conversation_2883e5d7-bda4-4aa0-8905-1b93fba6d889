"use client";

import { AppButton } from "@/app/components/app-button";
import { AppCode } from "@/app/components/app-code";
import { H2 } from "@/app/components/app-typography";
import { SetupEditForm } from "./setup-edit-form";
import { type EditFormType } from "./constants";

interface GovernanceFuzzingItem {
  id: string;
  address: string;
  chainId: number;
  eventDefinition: string;
  topic: string;
  recipes: Array<{
    fuzzerArgs?: {
      prepareContracts?: Array<{ target: string; replacement: string }>;
    };
  }>;
}

interface ExistingSetupsListProps {
  governanceFuzzing: GovernanceFuzzingItem[];
  isEditing: Map<string, boolean>;
  editForm: EditFormType | null;
  onEdit: (id: string) => void;
  onToggle: (id: string) => void;
  onDelete: (id: string) => void;
  onInputChange: (field: string, value: string) => void;
  onPrepareContractChange: (index: number, field: string, value: string) => void;
  onValidateEdit: () => void;
  onCancelEdit: (id: string) => void;
}

export function ExistingSetupsList({
  governanceFuzzing,
  isEditing,
  editForm,
  onEdit,
  onToggle,
  onDelete,
  onInputChange,
  onPrepareContractChange,
  onValidateEdit,
  onCancelEdit,
}: ExistingSetupsListProps) {
  if (!governanceFuzzing?.length) {
    return null;
  }

  return (
    <div className="space-y-6">
      <H2 className="text-accent-primary">
        Existing Governance Fuzzing Setups
      </H2>
      
      <div className="space-y-6">
        {governanceFuzzing.map((govFuzz) => (
          <div
            key={govFuzz.id}
            className="rounded-xl border border-stroke-neutral-decorative bg-back-neutral-tertiary p-6 shadow-sm"
          >
            {isEditing.get(govFuzz.id) && editForm ? (
              <SetupEditForm
                editForm={editForm}
                onInputChange={onInputChange}
                onPrepareContractChange={onPrepareContractChange}
                onValidate={onValidateEdit}
                onCancel={() => onCancelEdit(govFuzz.id)}
              />
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-fore-neutral-primary">
                    Contract: {govFuzz.address}
                  </h3>
                  <div className="flex gap-3">
                    <AppButton
                      type="button"
                      size="sm"
                      variant="secondary"
                      onClick={() => onEdit(govFuzz.id)}
                    >
                      Edit
                    </AppButton>
                    <AppButton
                      type="button"
                      size="sm"
                      onClick={() => onToggle(govFuzz.id)}
                    >
                      Toggle
                    </AppButton>
                    <AppButton
                      type="button"
                      size="sm"
                      variant="secondary"
                      onClick={() => onDelete(govFuzz.id)}
                      className="text-status-error"
                    >
                      Delete
                    </AppButton>
                  </div>
                </div>
                
                <AppCode
                  code={JSON.stringify(govFuzz, null, 2)}
                  language="json"
                />
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

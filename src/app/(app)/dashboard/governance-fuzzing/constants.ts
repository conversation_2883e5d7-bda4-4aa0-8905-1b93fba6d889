// Form styling constants following create job form patterns
export const FORM_STYLES = {
  // Responsive grid layout: single column on mobile, 2 columns on larger screens
  inputGroup: "mb-6 grid grid-cols-1 gap-6 xl:grid-cols-2",
  // Single input with min-width constraint
  input: "mb-[8px] min-w-[200px]",
  // Full width single column for special cases
  inputGroupSingle: "mb-6 grid grid-cols-1 gap-6",
  // Form container with responsive padding
  formContainer: "w-full space-y-6",
  // Field container with proper spacing
  fieldContainer: "w-full min-w-[200px]",
  divider: "h-px w-full bg-back-neutral-tertiary",
  sectionTitle: "text-xl font-bold leading-[1.3] text-fore-neutral-primary",
};

export type GitHubLinkFormValues = {
  contractAddress: string;
  chainId: number;
  recipeId: string;
  eventName: string;
  parameters: Array<{
    type: string;
    isIndexed: boolean;
    replacement?: string;
    unused: boolean;
  }>;
};

export type EditFormType = {
  address: string;
  chainId: string;
  eventDefinition: string;
  topic: string;
  prepareContracts: Array<{ target: string; replacement: string }>;
  id: string;
};

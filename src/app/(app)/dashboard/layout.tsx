import type { Viewport } from "next";
import type { ReactNode } from "react";

import { AppHeader } from "../components/app-header";
import { DashboardNavbar } from "./components/dashboard-navbar/dashboard-navbar";

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#060d1c" },
  ],
};

/** END SIDEBAR LINKS */

export default function DashboardLayout({ children }: { children: ReactNode }) {
  // TODO: Context, Pro vs Demo
  // Pro get's this, Demo doesn't
  // Or just initially, deploy Pro on separate URL to save time

  return (
    <>
      <AppHeader />
      <main className="flex min-h-[calc(100vh-73px)] w-full min-w-[600px] items-stretch">
        <DashboardNavbar />
        {children}
      </main>
    </>
  );
}

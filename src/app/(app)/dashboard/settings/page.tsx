"use client";

import { useState, useMemo } from "react";
import axios from "axios";

import { AppButton } from "@/app/components/app-button";
import { AppInput } from "@/app/components/app-input";
import { AppCode } from "@/app/components/app-code";
import { H1, H2, Body3 } from "@/app/components/app-typography";
import { useGetMyOrg } from "@/app/services/organization.hooks";
import { useGetShares } from "@/app/services/shares.hook";
import { searchObject } from "@/lib/utils";
import { InviteInProOrg } from "@/app/components/invide-pro-org";
import { MainContentWrapper } from "../../components/main-content-wrapper";

export default function Settings() {
  const { data: organization, isLoading: orgLoading } = useGetMyOrg();
  const { data: sharesData, refetch: refetchShares } = useGetShares();
  const [sharesQuery, setSharesQuery] = useState("");

  const deleteSharesHandler = async (shareId: string) => {
    try {
      const response = await axios({
        method: "POST",
        url: `/api/shares/delete`,
        data: { shareId },
      });

      if (response.status === 200) {
        console.log("Share deleted successfully");
        refetchShares();
      } else {
        alert("Something went wrong while deleting the share");
      }
    } catch (error) {
      console.error("Error deleting share:", error);
      alert("Failed to delete share");
    }
  };

  const filteredShares = useMemo(() => {
    if (!sharesData) return [];

    return sharesData.filter((share) => {
      if (!sharesQuery) return true;

      const queryWords = sharesQuery.toLowerCase().split(/\s+/);
      return queryWords.every((word) => searchObject(share, word));
    });
  }, [sharesData, sharesQuery]);

  return (
    <MainContentWrapper>
      <H1 className="mb-8 text-accent-primary">Settings</H1>

      {/* Organization Information Section */}
      <div className="mb-12">
        <H2 className="mb-6" color="primary">
          Organization Information
        </H2>

        {orgLoading ? (
          <div className="h-4 w-32 animate-pulse rounded bg-back-neutral-tertiary" />
        ) : organization ? (
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <Body3 color="secondary" className="font-semibold">
                Organization ID:
              </Body3>
              <Body3 color="primary" className="font-mono">
                {organization.id}
              </Body3>
            </div>
            <div className="flex items-center gap-4">
              <Body3 color="secondary" className="font-semibold">
                Organization Name:
              </Body3>
              <Body3 color="primary">{organization.name || "Not set"}</Body3>
            </div>
            <div className="flex items-center gap-4">
              <Body3 color="secondary" className="font-semibold">
                Billing Status:
              </Body3>
              <Body3 color="primary">{organization.billingStatus}</Body3>
            </div>
            {/* {organization.totalMinutesLeft && (
                <div className="flex items-center gap-4">
                  <Body3 color="secondary" className="font-semibold">
                    Minutes Remaining:
                  </Body3>
                  <Body3 color="primary">{organization.totalMinutesLeft}</Body3>
                </div>
              )} */}
          </div>
        ) : (
          <Body3 color="secondary">No organization found</Body3>
        )}
      </div>

      {/* Invite Colleagues Section */}
      <div className="mb-12">
        <H2 className="mb-6" color="primary">
          Invite Colleagues
        </H2>
        <InviteInProOrg />
      </div>

      {/* Manage Shares Section */}
      <div className="mb-12">
        <H2 className="mb-6" color="primary">
          Manage Shares
        </H2>
        <Body3 color="secondary" className="mb-4">
          This section lists all of the jobs you're sharing. You can delete a
          share to make the information private.
        </Body3>

        <div className="mb-6">
          <AppInput
            type="text"
            placeholder="Search by id, repoName, orgName, ref, label..."
            value={sharesQuery}
            onChange={(e) => setSharesQuery(e.target.value)}
            className="max-w-md"
          />
        </div>

        <div className="space-y-4">
          {filteredShares?.map((share) => (
            <div
              key={share.id}
              className="flex items-center gap-6 rounded-lg border border-stroke-neutral-decorative bg-back-neutral-tertiary p-4"
            >
              <div className="flex-1">
                <AppCode
                  code={JSON.stringify(share, null, 2)}
                  language="json"
                  extraCss="max-h-48 overflow-y-auto"
                />
              </div>
              <AppButton
                onClick={() => deleteSharesHandler(share.id)}
                variant="secondary"
                size="sm"
              >
                Delete
              </AppButton>
            </div>
          ))}

          {filteredShares?.length === 0 && (
            <Body3 color="secondary">
              {sharesQuery
                ? "No shares match your search."
                : "No shares found."}
            </Body3>
          )}
        </div>
      </div>
    </MainContentWrapper>
  );
}

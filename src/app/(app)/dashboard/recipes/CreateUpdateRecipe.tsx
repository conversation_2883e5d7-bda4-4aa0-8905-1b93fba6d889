"use client";

import axios from "axios";
import { useEffect, useState } from "react";
import { Form<PERSON>rovider, useFieldArray } from "react-hook-form";

import { ENV_TYPE } from "@/app/app.constants";
import {
  useGetRecipeByIdentifier,
  useGetRecipes,
} from "@/app/services/recipes.hook";

import {
  SubFormHalmos,
  SubFormHalmosAdvanced,
} from "@/app/components/create-job-form/subform-halmos";
import { AppButton } from "../../../components/app-button";
import { AppInput } from "../../../components/app-input";
import { AppSpinner } from "../../../components/app-spinner";
import { AppSwitch } from "../../../components/app-switch";
import { Body3, H2 } from "../../../components/app-typography";
import {
  SubFormEchidna,
  SubFormEchidnaAdvanced,
} from "../../../components/create-job-form/subform-echidna";
import {
  SubFormMedusa,
  SubFormMedusaAdvanced,
} from "../../../components/create-job-form/subform-medusa";
import {
  SubFormFoundry,
  SubFormFoundryAdvanced,
} from "@/app/components/create-job-form/subform-foundry";
import {
  SubFormKontrol,
  SubFormKontrolAdvanced,
} from "@/app/components/create-job-form/subform-kontrol";
import { JobTypeFuzzer } from "../../../components/job-type-fuzzer";

import { useFormLogic } from "@/app/components/create-job-form/hooks/useFormLogic";
import type {
  GitHubLinkFormValues,
  RecipeFormProps,
} from "@/app/components/create-job-form/types";
import {
  parseGitHubURL,
  applyRecipeDefaults,
} from "@/app/components/create-job-form/utils";
import { FORM_STYLES } from "@/app/components/create-job-form/constants";

export function CreateUpdateRecipeForm({
  editId,
  title = "Create Recipe",
  submitLabel = "Create Recipe",
}: RecipeFormProps) {
  const [showDynamicReplacement, setShowDynamicReplacement] = useState(false);
  const {
    form,
    register,
    handleSubmit,
    setValue,
    setError,
    watch,
    errors,
    isSubmitting,
  } = useFormLogic("recipe");

  const { refetch: refetchRecipes, data: recipes } = useGetRecipes();

  const { control } = form;
  const { fields, append, remove } = useFieldArray({
    control,
    name: "fields",
  });

  const watchedFields = watch("fields");

  useEffect(() => {
    // Filter out incomplete field groups
    if (!watchedFields) return;

    const validFields = watchedFields.filter(
      (field) =>
        field.variableName.trim() &&
        field.interface.trim() &&
        field.value.trim()
    );

    const prepContract = validFields.map((field) => ({
      target: `${field.variableName} = ${field.interface}`,
      replacement: `${field.variableName} = ${field.interface}(${field.value});`,
      endOfTargetMarker: "[^;]*",
      targetContract: "Setup.sol",
    }));
    setValue("prepareContracts", prepContract);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watchedFields, form.getValues()]);

  const onSubmit = async ({
    orgName,
    repoName,
    ref,
    directory,
    medusaConfig,
    timeout,
    preprocess,
    displayName,

    pathToTester,
    contract,
    corpusDir,
    echidnaConfig,
    forkBlock,
    forkMode,
    forkReplacement,
    rpcUrl,
    testLimit,
    testMode,
    targetCorpus,

    runs,
    seed,
    verbosity,
    testCommand,
    testTarget,

    halmosArray,
    halmosLoops,
    halmosPrefix,

    kontrolTest,
    prepareContracts,
  }: GitHubLinkFormValues) => {
    const fuzzerArgs =
      env == ENV_TYPE.MEDUSA
        ? {
            timeout,
            config: medusaConfig,
            targetCorpus,
            preprocess,
          }
        : env == ENV_TYPE.ECHIDNA
          ? {
              pathToTester,
              config: echidnaConfig,
              contract,
              corpusDir,
              forkBlock,
              forkMode,
              forkReplacement,
              rpcUrl,
              testLimit,
              testMode,
              targetCorpus,
              preprocess,
            }
          : env == ENV_TYPE.FOUNDRY
            ? {
                contract,
                forkBlock,
                forkMode,
                rpcUrl,
                runs,
                seed,
                testCommand,
                testTarget,
                verbosity,
                preprocess,
                prepareContracts,
              }
            : env == ENV_TYPE.HALMOS
              ? {
                  contract,
                  testCommand,
                  verbosity,
                  halmosArray,
                  halmosLoops,
                  halmosPrefix,
                  timeout,
                  preprocess,
                }
              : {
                  kontrolTest,
                  timeout,
                  preprocess,
                };

    // TODO: CONDITION: If we have `editId` then this is an update
    // Else it's a create

    if (editId) {
      const recipeData = {
        fuzzer: env,
        displayName,
        fuzzerArgs,
        preprocess,
        orgName,
        repoName,
        ref,
        directory,
      };

      try {
        // This is a UPDATE
        const foundData = await axios({
          method: "POST",
          url: `/api/recipes/${editId}`,
          data: {
            recipeId: editId,
            recipeData,
          },
        });
        alert("Updated recipe!");
      } catch (e) {
        console.log("e", e);
        alert(`Something went wrong: ${e.response.data.message}`);
      }
    }

    // It's a create
    if (!editId) {
      try {
        const foundData = await axios({
          method: "POST",
          url: `/api/recipes`,
          data: {
            fuzzer: env, /// @audit Necessary
            displayName,
            fuzzerArgs,
            preprocess,
            orgName,
            repoName,
            ref,
            directory,
          },
        });

        refetchRecipes();
      } catch (e) {
        console.log("e", e);
        alert(`Something went wrong: ${e.response.data.message}`);
      }
    }
  };

  const recipe = useGetRecipeByIdentifier(editId);
  const [doneLoading, setDoneLoading] = useState(false);

  // UPDATE RECIPE
  // We can pass an ID, when the ID is passed that means we're updating tge
  useEffect(() => {
    async function setPresets() {
      console.log("recipe", recipe);

      // @ts-expect-error we'd have to cast and verify but this is always valid
      setEnv(recipe.data.fuzzer);

      Object.keys(recipe.data).forEach((key) => {
        console.log("key", key);
        // @ts-expect-error we purposefully don't care, since the form will ignore extra fields anyway
        setValue(key.toString(), recipe.data[key]);
      });

      Object.keys(recipe.data?.fuzzerArgs).forEach((key) => {
        console.log("key", key);
        // @ts-expect-error See above
        setValue(key.toString(), recipe.data?.fuzzerArgs[key]);
      });
    }

    if (recipe?.data?.fuzzer && !doneLoading) {
      setPresets();
      setDoneLoading(true);
    }
  }, [recipe, doneLoading, setValue]);

  const [env, setEnv] = useState(ENV_TYPE.MEDUSA);

  const githubUrlRegister = register("githubURL");

  return (
    <FormProvider
      {...({
        register,
        handleSubmit,
        setValue,
        setError,
        watch,
        errors,
        isSubmitting,
      } as any)}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <h3 className="mb-[22px] text-[28px] leading-[33px] text-fore-neutral-primary">
          {title}
        </h3>

        {!editId && recipes && (
          <div className="my-[20px] flex flex-wrap gap-[20px]">
            {recipes.map((recipe) => (
              <AppButton
                onClick={() => applyRecipeDefaults(recipe, setValue, setEnv)}
                className=" px-[14px] py-[9px] leading-[21px]"
                key={recipe.displayName}
              >
                Use Default `{recipe.displayName}`
              </AppButton>
            ))}
          </div>
        )}

        <div className="mb-6 w-full">
          {env && (
            <JobTypeFuzzer value={env} onChange={(value) => setEnv(value)} />
          )}
        </div>

        <div className={FORM_STYLES.formContainer}>
          <div className="space-y-6">
            <div className={FORM_STYLES.inputGroupSingle}>
              <div className={FORM_STYLES.fieldContainer}>
                <AppInput
                  className={FORM_STYLES.input}
                  label="Recipe Display Name"
                  {...register("displayName")}
                  type="text"
                />
              </div>
            </div>

            <div className={FORM_STYLES.inputGroupSingle}>
              <div className={FORM_STYLES.fieldContainer}>
                <AppInput
                  className={FORM_STYLES.input}
                  {...githubUrlRegister}
                  onChange={(e) => {
                    githubUrlRegister.onChange(e); // default react-hook-form onChange
                    parseGitHubURL(e.target.value, setValue, setError); // additional logic for parsing URL
                  }}
                  type="text"
                  label="GitHub Repo URL"
                  placeholder="Enter GitHub Repo URL"
                  error={errors.githubURL?.message}
                />
              </div>
            </div>
          </div>

          <div className={FORM_STYLES.divider} />

          <div className="space-y-4">
            <h3 className={FORM_STYLES.sectionTitle}>
              Or specify the organization, repository and branch directly
            </h3>

            <div className="space-y-6">
              <div className={FORM_STYLES.inputGroup}>
                <div className={FORM_STYLES.fieldContainer}>
                  <AppInput
                    className={FORM_STYLES.input}
                    label="Organization"
                    {...register("orgName")}
                    type="text"
                  />
                </div>
                <div className={FORM_STYLES.fieldContainer}>
                  <AppInput
                    className={FORM_STYLES.input}
                    {...register("repoName")}
                    type="text"
                    label="Repo"
                  />
                </div>
              </div>

              <div className={FORM_STYLES.inputGroup}>
                <div className={FORM_STYLES.fieldContainer}>
                  <AppInput
                    className={FORM_STYLES.input}
                    {...register("ref")}
                    type="text"
                    label="Branch"
                  />
                </div>
                <div className={FORM_STYLES.fieldContainer}>
                  <AppInput
                    className={FORM_STYLES.input}
                    {...register("directory")}
                    type="text"
                    label="Directory"
                  />
                </div>
              </div>
            </div>
          </div>

          {!env && (
            <div className={FORM_STYLES.inputGroupSingle}>
              <div className={FORM_STYLES.fieldContainer}>
                <AppInput
                  className={FORM_STYLES.input}
                  {...register("customOut")}
                  type="text"
                  label="Custom output folder"
                />
              </div>
            </div>
          )}

          <div className={FORM_STYLES.divider} />

          {env && (
            <div className="space-y-4">
              <h3 className={FORM_STYLES.sectionTitle}>
                Configure custom parameters for {env.toLowerCase()}:
              </h3>

              <div className="space-y-8">
                {env === ENV_TYPE.MEDUSA ? (
                  <SubFormMedusa />
                ) : env === ENV_TYPE.ECHIDNA ? (
                  <SubFormEchidna />
                ) : env === ENV_TYPE.FOUNDRY ? (
                  <SubFormFoundry />
                ) : env === ENV_TYPE.HALMOS ? (
                  <SubFormHalmos />
                ) : (
                  <SubFormKontrol />
                )}
              </div>
            </div>
          )}

          <div className={FORM_STYLES.divider} />

          {env && (
            <div className="space-y-4">
              <h3 className={FORM_STYLES.sectionTitle}>
                Advanced Configuration:
              </h3>

              <div className="space-y-8">
                {env === ENV_TYPE.MEDUSA ? (
                  <SubFormMedusaAdvanced />
                ) : env === ENV_TYPE.ECHIDNA ? (
                  <SubFormEchidnaAdvanced />
                ) : env === ENV_TYPE.FOUNDRY ? (
                  <SubFormFoundryAdvanced />
                ) : env === ENV_TYPE.HALMOS ? (
                  <SubFormHalmosAdvanced />
                ) : (
                  <SubFormKontrolAdvanced />
                )}
              </div>
            </div>
          )}

          <div className={FORM_STYLES.divider} />

          <div className="mb-6 flex items-center gap-4">
            <Body3 color="secondary" className="font-semibold">
              Enable Dynamic Replacement:
            </Body3>
            <AppSwitch
              enabled={showDynamicReplacement}
              onChange={(e) => setShowDynamicReplacement(e.target.checked)}
            />
          </div>

          {showDynamicReplacement && (
            <>
              <div className="border-status-warning bg-status-warning/10 mb-6 rounded-lg border p-4">
                <H2 className="mb-2" color="primary">
                  Dynamic Replacement Mode
                </H2>
                <div className="space-y-2">
                  <Body3 color="secondary">
                    • Dynamic Replacement is in EXPERIMENTAL mode
                  </Body3>
                  <Body3 color="secondary">
                    • All variables Dynamically Replaced MUST be in the
                    `Setup.sol` file
                  </Body3>
                  <Body3 color="secondary">
                    • Make sure you have no clashing file!
                  </Body3>
                </div>
              </div>
              <div className="space-y-4">
                <div className="space-y-4">
                  {fields.map((field, index) => (
                    <div
                      key={field.id}
                      className={FORM_STYLES.inputGroupTriple}
                    >
                      <div className={FORM_STYLES.fieldContainer}>
                        <AppInput
                          className={FORM_STYLES.input}
                          label="Variable Name"
                          {...register(`fields.${index}.variableName` as const)}
                          type="text"
                          defaultValue={field.variableName}
                        />
                      </div>
                      <div className={FORM_STYLES.fieldContainer}>
                        <AppInput
                          className={FORM_STYLES.input}
                          label="Interface"
                          {...register(`fields.${index}.interface` as const)}
                          type="text"
                          defaultValue={field.interface}
                        />
                      </div>
                      <div className={FORM_STYLES.fieldContainer}>
                        <AppInput
                          className={FORM_STYLES.input}
                          label="Value"
                          {...register(`fields.${index}.value` as const)}
                          type="text"
                          defaultValue={field.value}
                        />
                        <button
                          type="button"
                          onClick={() => remove(index)}
                          className="mt-2 text-sm text-accent-primary underline hover:text-accent-secondary"
                          title="Delete Field Group"
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                <AppButton
                  type="button"
                  variant="secondary"
                  onClick={() =>
                    append({ variableName: "", interface: "", value: "" })
                  }
                  className="mt-4"
                >
                  Add More Fields
                </AppButton>
              </div>
            </>
          )}

          <AppButton
            type="submit"
            disabled={isSubmitting}
            size="lg"
            fullWidth
            className="mx-auto flex max-w-[400px] flex-row items-center justify-center gap-1"
          >
            {isSubmitting ? <AppSpinner /> : submitLabel}
          </AppButton>
        </div>
      </form>
    </FormProvider>
  );
}

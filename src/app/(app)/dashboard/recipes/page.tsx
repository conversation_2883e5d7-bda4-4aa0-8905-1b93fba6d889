"use client";

import { AppPageHeader } from "../../components/app-page-header";
import { MainContentWrapper } from "../../components/main-content-wrapper";
import { AllRecipes } from "./AllRecipes";
import { CreateUpdateRecipeForm } from "./CreateUpdateRecipe";

export default function Campaigns() {
  return (
    <MainContentWrapper>
      <AppPageHeader
        title="Recipes"
        descriptions={[
          "Recipes are Job Configurations. They can be used as:",
          "• Presets in the Jobs Page",
          "• Template for Campaigns",
          "Recipes appear as buttons in the Jobs Page and can be used with Campaigns to automatically run on PR or Commit",
        ]}
      />

      <CreateUpdateRecipeForm />
      <AllRecipes />
    </MainContentWrapper>
  );
}

import { useMemo } from "react";
import {
  FaBook,
  FaCheckCircle,
  FaDiscord,
  FaFolderOpen,
  FaGlobe,
  FaLock,
  FaMagic,
  FaScroll,
  FaSignOutAlt,
} from "react-icons/fa";
import { Fa<PERSON>rrowRotateRight, FaGear } from "react-icons/fa6";
import { GiMagicHat } from "react-icons/gi";
import { IoIosSwitch } from "react-icons/io";
import { IoBagSharp, IoShieldCheckmarkSharp } from "react-icons/io5";
import { PiFlowArrowFill } from "react-icons/pi";

import { useGetAbi } from "@/app/services/abi.hook";
import { useGetDiscord } from "@/app/services/discord.hook.ts";
import { OrgStatus, useGetMyOrg } from "@/app/services/organization.hooks";
import { useGetRecurring } from "@/app/services/recurring.hook";
import type { AbiApiData } from "@/app/types/abi.api";

const mapChildItems = (abis: AbiApiData[]) =>
  abis.map((project) => ({
    label: project.identifier,
    href: `/dashboard/handlers/${project.identifier}`,
    icon: FaFolderOpen,
  }));

const makeFreeSidebarLinks = (abis: AbiApiData[], discordUrl?: string) => {
  const items = [
    {
      label: "Build your Handlers (new)",
      icon: FaFolderOpen,
      type: "link",
      href: "/dashboard/build",
    },
    {
      label: "Saved Handlers",
      icon: IoIosSwitch,
      type: "button",
      href: "/dashboard/handlers",
      childItems: mapChildItems(abis),
    },
    {
      label: "Get PRO",
      icon: IoShieldCheckmarkSharp,
      type: "link",
      href: "/dashboard/pro",
    },
    {
      label: "Discord",
      icon: FaDiscord,
      type: "link",
      href: discordUrl || "https://discord.gg/6JnUcp33Yn",
      newTab: true,
    },
    {
      label: "Tutorials",
      icon: FaBook,
      type: "link",
      href: "https://book.getrecon.xyz/",
      newTab: true,
    },
    {
      label: "Logout",
      icon: FaSignOutAlt,
      type: "logout",
    },
  ];
  // Remove Saved Handlers for new accounts
  if (abis.length == 0) {
    return items.filter((item) => item.label !== "Saved Handlers");
  }
  return items;
};

const makeProSidebarLinks = (
  abis: AbiApiData[],
  discordUrl?: string,
  recurringJobsCount?: number
) => {
  const items = [
    {
      label: "Build your Handlers (new)",
      icon: FaFolderOpen,
      type: "link",
      href: "/dashboard/build",
    },
    {
      label: "Saved Handlers",
      icon: IoIosSwitch,
      type: "button",
      childItems: mapChildItems(abis),
    },
    {
      label: "Jobs",
      icon: IoBagSharp,
      type: "link",
      href: "/dashboard/jobs",
    },
    {
      label: "Recon Magic",
      icon: FaMagic,
      type: "link",
      href: "/dashboard/magic",
    },
    {
      label: "Recipes & Alerts",
      icon: FaScroll,
      type: "link",
      href: "/dashboard/recipes",
    },
    {
      label: "Campaigns",
      icon: FaGlobe,
      type: "link",
      href: "/dashboard/campaigns",
    },
    // {
    //   label: "Recurring Jobs",
    //   icon: FaArrowRotateRight,
    //   type: "link",
    //   href: "/dashboard/recurring",
    // },
    // {
    //   label: "Live Monitoring",
    //   icon: FaCheckCircle,
    //   type: "link",
    //   href: "/dashboard/monitoring",
    // },
    {
      label: "Governance Fuzzing",
      icon: GiMagicHat,
      type: "link",
      href: "/dashboard/governance-fuzzing",
    },
    {
      label: "Private Repos",
      icon: FaLock,
      type: "link",
      href: "/dashboard/installs",
    },
    {
      label: "Settings",
      icon: FaGear,
      type: "link",
      href: "/dashboard/settings",
    },
    {
      label: "Discord",
      icon: FaDiscord,
      type: "link",
      href: discordUrl || "https://discord.gg/6JnUcp33Yn",
      newTab: true,
    },
    {
      label: "Tutorials",
      icon: FaBook,
      type: "link",
      href: "https://book.getrecon.xyz/",
      newTab: true,
    },
    {
      label: "Logout",
      icon: FaSignOutAlt,
      type: "logout",
    },
  ];

  let filteredItems = items;

  // Remove Saved Handlers for new accounts
  if (abis.length == 0) {
    filteredItems = filteredItems.filter(
      (item) => item.label !== "Saved Handlers"
    );
  }

  // Remove Recurring Jobs if there are 0 recurring jobs
  if (recurringJobsCount === 0) {
    filteredItems = filteredItems.filter(
      (item) => item.label !== "Recurring Jobs"
    );
  }

  return filteredItems;
};

const makeNoOrgSidebarLinks = () => [
  {
    label: "Create an Account",
    icon: PiFlowArrowFill,
    type: "link",
    href: "/dashboard/onboard",
  },
];

export const useGetSidebarLinks = () => {
  const { orgStatus } = useGetMyOrg();
  const { data: abiData, isLoading: isChildItemsLoading } = useGetAbi();
  const { data: discordUrl } = useGetDiscord();
  const { data: recurringJobs } = useGetRecurring();

  const items = useMemo(() => {
    const abiDataFinal = abiData ?? [];
    const recurringJobsCount = recurringJobs?.length ?? 0;

    const MAP = {
      [OrgStatus.PAID]: makeProSidebarLinks(
        abiDataFinal,
        discordUrl,
        recurringJobsCount
      ),
      [OrgStatus.TRIAL]: makeProSidebarLinks(
        abiDataFinal,
        discordUrl,
        recurringJobsCount
      ),
      [OrgStatus.FREE]: makeFreeSidebarLinks(abiDataFinal, discordUrl),
    };

    return !orgStatus ? makeNoOrgSidebarLinks() : MAP[orgStatus] || [];
  }, [abiData, orgStatus, discordUrl, recurringJobs]);

  return { items, isChildItemsLoading };
};

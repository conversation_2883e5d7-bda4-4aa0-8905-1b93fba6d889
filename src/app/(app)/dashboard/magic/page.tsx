"use client";

import { <PERSON>pp<PERSON><PERSON><PERSON> } from "@/app/components/app-button";
import { AppInput } from "@/app/components/app-input";
import { AppPageHeader } from "../../components/app-page-header";
import { useGetMyOrg } from "@/app/services/organization.hooks";
import { useEffect, useState } from "react";
import axios from "axios";
import { AppCode } from "@/app/components/app-code";
import { AppRadioGroup } from "@/app/components/app-radio-group";

import { Body3, Title3Strong } from "@/app/components/app-typography";
import { FORM_STYLES } from "@/app/components/create-job-form/constants";
import { AppInputDropdown } from "@/app/components/app-input-dropdown";

// ADD check for for fork replay ? (vm warp and roll )

const workflowRadioOptions = [
  {
    label: "Scaffold Invariant Tests to coverage",
    value: "phase-2",
  },
  {
    label: "Setup Unit Tests for one contract",
    value: "unit-0",
  },
  {
    label: "Identify Invariants and write tests(Coming VERY Soon)",
    value: null,
  },
  {
    label: "Find me some Bugs (Coming Soon)",
    value: null,
  },
];

export default function Magic() {
  const checkCanClone = async (repo: string, org: string): Promise<boolean> => {
    try {
      await axios({
        method: "POST",
        url: `/api/jobs/canclone`,
        data: {
          orgName: org,
          repoName: repo,
        },
      });
      return true;
    } catch {
      console.log("Can't clone this repo");
    }

    return false;
  };

  const [orgName, setOrgName] = useState("");
  const [repoName, setRepoName] = useState("");
  const [branch, setBranch] = useState("");
  const { data: organization } = useGetMyOrg();

  const [workflowType, setWorkflowType] = useState("phase-2");
  const [githubURL, setGithubURL] = useState("");
  const [allContracts, setAllContracts] = useState([]);
  const [contractName, setContractName] = useState("");

  const [isLoading, setIsLoading] = useState(false);
  const [isButtonLoading, setIsButtonLoading] = useState(false);

  const [allMagicJobs, setAllMagicJobs] = useState([]);

  const handleWorkflowType = (e: string | null) => {
    if (e === null) return;
    setWorkflowType(e);
  };

  const parseURI = (inputValue: string) => {
    console.log("input value", inputValue);
    const success = /^(https?:\/\/)?(www\.)?github\.com\/.+\/.+(\.git)?$/.test(
      inputValue
    );

    if (!success) {
      alert("Invalid GitHub URL");
      return;
    }

    const ghLink = inputValue.endsWith("/")
      ? inputValue.slice(0, -1)
      : inputValue;
    const uriParts = ghLink
      .replace("https://", "")
      .replace("http://", "")
      .split("/");

    if (uriParts.length >= 3) {
      const orgName = uriParts[1];
      const repoName = uriParts[2];
      let ref = "main"; // Default branch

      // Check if the URL specifies a branch
      if (uriParts.length > 5 && uriParts[3] === "tree") {
        // The branch name can include slashes, so we join the remaining parts
        ref = uriParts.slice(4).join("/");
      } else if (uriParts.length === 5 && uriParts[3] === "tree") {
        // Handle the case where there's no slash in the branch name
        ref = uriParts[4];
      }

      // Set the values to the form
      setOrgName(orgName);
      setRepoName(repoName);
      setBranch(ref);
    }
  };

  const fetchAllMagicJobs = async () => {
    const response = await axios.get("/api/claude/jobs");
    setAllMagicJobs(response.data.data);
  };

  useEffect(() => {
    fetchAllMagicJobs();
  }, []);

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (isLoading) return;

    if (!organization) {
      alert("You must be a member of an organization to use this feature");
      return;
    }

    if (!orgName) {
      alert("You must enter a repo name to use this feature");
      return;
    }

    if (!repoName) {
      alert("You must enter a repo name to use this feature");
      return;
    }

    if (!branch) {
      alert("You must enter a branch name to use this feature");
      return;
    }

    setIsLoading(true);

    // Already caught in the checkCanClone function
    const canClone = await checkCanClone(repoName, orgName);
    if (!canClone) {
      alert(
        "Recon doesn't have access to this repository, please authorize the Recon app on the repo"
      );
      setIsLoading(false);
      return;
    }

    // Unit test extra validation
    if (workflowType === "unit-0") {
      if (!contractName) {
        alert("You must enter a contract name to use this feature");
        setIsLoading(false);
        return;
      }
    }

    let optionalParams = {};
    if (workflowType === "unit-0") {
      optionalParams = { contractName };
    }
    try {
      const response = await axios.post("/api/claude/jobs", {
        organizationId: organization?.id,
        orgName,
        repoName,
        branch,
        workflowType,
        optionalParams,
      });

      console.log(response);
    } catch (error) {
      console.error(error);
    }

    setIsLoading(false);
    setAllContracts([]);
    setContractName("");
    setOrgName("");
    setRepoName("");
    setBranch("");
    setGithubURL("");
    fetchAllMagicJobs();
  }

  const deleteMagicJobHandler = async (jobId: string) => {
    try {
      setIsButtonLoading(true);
      const response = await axios.delete("/api/claude/jobs", {
        data: {
          jobId,
        },
      });
      console.log(response);
    } catch (error) {
      console.error(error);
      alert(`Failed to delete job:\n ${error.response.data.message}`);
    }
    fetchAllMagicJobs();
    setIsButtonLoading(false);
  };

  const fetchAllContracts = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsButtonLoading(true);
    try {
      const response = await axios.get(
        `/api/github/${orgName}/${repoName}/${branch}`
      );
      setAllContracts(response.data.data);
    } catch (error) {
      console.error(error);
      alert(`Failed to fetch contracts:\n ${error.response.data.message}`);
    }
    setIsButtonLoading(false);
  };

  return (
    <div className="flex flex-col gap-6 px-[45px] py-6">
      <AppPageHeader
        title="Recon Magic"
        descriptions={[
          "Give us a Repo with scaffolded Chimera Invariant Tests, we'll get them to coverage",
          "NOTE: Only super admin can use this feature! DM us for an early preview!",
        ]}
      />

      <div className="w-full space-y-6">
        <form onSubmit={handleSubmit} className="flex flex-col gap-6">
          <div className="grid grid-cols-1 gap-6 xl:grid-cols-2">
            <AppInput
              label="Paste Github URL for your convenience"
              onChange={(e) => {
                parseURI(e.target.value); // additional logic for parsing URL
                setGithubURL(e.target.value);
              }}
              type="text"
              placeholder="Enter GitHub Repo URL"
              error={""}
              value={githubURL}
            />
          </div>

          <div className="grid grid-cols-1 gap-6 xl:grid-cols-2">
            <AppInput
              label="OrgName"
              value={orgName}
              onChange={(e) => setOrgName(e.target.value)}
            />
            <AppInput
              label="RepoName"
              value={repoName}
              onChange={(e) => setRepoName(e.target.value)}
            />
          </div>

          <div className="grid grid-cols-1 gap-6 xl:grid-cols-2">
            <AppInput
              label="Branch"
              value={branch}
              onChange={(e) => setBranch(e.target.value)}
            />
          </div>

          <div className="h-px w-full bg-stroke-neutral-decorative" />

          <div className="flex flex-col gap-4">
            <Body3 color="secondary">Select Job Type</Body3>
            <AppRadioGroup
              name="WorkflowType"
              options={workflowRadioOptions}
              onChange={(e) => handleWorkflowType(e)}
              value={workflowType}
            />
          </div>
          {workflowType === "unit-0" && (
            <div className="flex flex-col gap-4">
              <div className="items-middle justify-middle vertical-middle grid grid-cols-1 gap-6 xl:grid-cols-2">
                <div>
                  <AppInputDropdown
                    className={FORM_STYLES.input}
                    value={contractName}
                    type="text"
                    label="Contract"
                    placeholder="Search by name, ID, or repository..."
                    dropdownItems={allContracts.map((rec) => ({
                      id: rec.path,
                      label: `${rec.path}`,
                      fields: rec,
                    }))}
                    onItemSelect={(path) => setContractName(path as string)}
                  />
                </div>

                <div className="flex items-center xl:justify-start">
                  <AppButton
                    onClick={fetchAllContracts}
                    disabled={isButtonLoading}
                  >
                    {isButtonLoading ? "Loading..." : "Refetch Contracts"}
                  </AppButton>
                </div>
              </div>
            </div>
          )}

          <div className="flex">
            <AppButton type="submit" disabled={isLoading}>
              {isLoading ? "Loading..." : "Start Job"}
            </AppButton>
          </div>
        </form>
      </div>

      <div className="w-full space-y-6">
        <Title3Strong>All Magic Jobs</Title3Strong>
        <div className="flex flex-col gap-6">
          {allMagicJobs.map((job) => (
            <div
              key={job.id}
              className="rounded-lg border border-stroke-neutral-decorative bg-back-neutral-tertiary p-6"
            >
              <AppCode code={JSON.stringify(job, null, 2)} language="json" />
              <div className="mt-6 flex justify-end">
                <AppButton
                  disabled={isButtonLoading}
                  onClick={() => deleteMagicJobHandler(job.id)}
                  variant="outline"
                >
                  {isButtonLoading
                    ? "Loading"
                    : "Delete this Job (only b4 it starts)"}
                </AppButton>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

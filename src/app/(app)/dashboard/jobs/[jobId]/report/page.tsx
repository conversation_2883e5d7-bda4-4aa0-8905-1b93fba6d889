"use client";
import axios from "axios";
import { use, useEffect, useState } from "react";

import { MainContentWrapper } from "@/app/(app)/components/main-content-wrapper";
import { downloadFile } from "@/app/(app)/tools/mdReportHelper";
import { AppButton } from "@/app/components/app-button";
import { H2 } from "@/app/components/app-typography";
import JobReport from "@/app/components/JobReport/JobReport";
import { useGetJobById } from "@/app/services/jobs.hooks";
import type { Fuzzer, FuzzingResults } from "@recon-fuzz/log-parser";
import { generateJobMD, processLogs } from "@recon-fuzz/log-parser";
import Link from "next/link";

export default function ShareReport({
  params,
}: {
  params: Promise<{ jobId: string }>;
}) {
  const { jobId } = use(params);
  const [md, setMd] = useState("");
  const [jobStats, setJobStats] = useState<FuzzingResults | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { data: jobData } = useGetJobById(jobId);

  useEffect(() => {
    async function getLogs() {
      const jobLogsRaw = await axios({
        method: "POST",
        url: `/api/fetchLogs`,
        data: {
          logsUrl: jobData?.logsUrl,
        },
      });
      const data = processLogs(jobLogsRaw.data, jobData?.fuzzer as Fuzzer);
      const jobStats = data;
      setJobStats(jobStats);

      const md = generateJobMD(
        jobData?.fuzzer as Fuzzer,
        jobLogsRaw.data,
        jobData?.label || `${jobData?.orgName}/${jobData?.repoName}`
      );
      setMd(md);
      setIsLoading(false);
    }
    getLogs();
  }, [jobData]);
  console.log("jobStats", jobStats);

  return (
    <MainContentWrapper>
      {isLoading === false ? (
        <>
          <H2 className="mb-6 text-accent-primary">
            Report for {jobData?.orgName}/{jobData?.repoName}/{jobData?.ref}
          </H2>
          <div className="mb-8 flex flex-row justify-between">
            {md ? (
              <AppButton onClick={() => downloadFile(md, jobData.repoName)}>
                Download report
              </AppButton>
            ) : null}
            <Link href={`/dashboard/jobs/${jobId}`}>
              <AppButton variant="secondary">Back to Details</AppButton>
            </Link>
          </div>
          {md && jobData && jobStats ? (
            <JobReport
              fuzzer={jobData.fuzzer}
              jobStats={jobStats}
              showBrokenProp={true}
            />
          ) : null}
        </>
      ) : (
        <div className="flex min-h-[calc(100vh-73px)] items-center justify-center">
          <H2 className="text-fore-neutral-primary">Loading...</H2>
        </div>
      )}
    </MainContentWrapper>
  );
}

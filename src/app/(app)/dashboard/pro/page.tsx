"use client";

import Image from "next/image";
import Link from "next/link";

import { AppButton } from "@/app/components/app-button";
import { AppListCheckItem } from "@/app/components/app-list-check-item";

import { AppPageHeader } from "../../components/app-page-header";
import styles from "./page.module.scss";

const renderListItem = (text) => {
  return <AppListCheckItem text={text} key={text} />;
};

export default function Onboard() {
  return (
    <div className="min-h-screen grow overflow-y-auto bg-back-neutral-secondary pl-[80px] pt-[40px] text-fore-neutral-primary dark:bg-back-neutral-primary">
      <div className={styles.columns}>
        <div>
          <AppPageHeader title="RECON PRO" descriptions={[]} />
          <iframe
            width="560"
            height="315"
            title="Recon Pro Overview"
            src="https://www.youtube.com/embed/FUrBKpTvM1Q?si=i-48VGB7IQ36gx2X"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowFullScreen
          ></iframe>
          <div className="mt-[22px]"></div>
          <p>
            Recon PRO allows you to run invariant test in the Cloud, on PR,
            Commit or whenever you want
          </p>

          <ul className="text-[16px] leading-[26px]">
            {[
              "Add Public and Private Repos to your Organization and Users",
              "Run multiple fuzzers in the cloud, simultaneously",
              "Automated Runs on PR or Commit",
              "Automated Broken Invariants Test Generation",
              "Advanced Builder",
              "Store Recipes of your common jobs",
              "One click shareable Job Reports for your SRs",
              "Private Coaching",
            ].map(renderListItem)}
          </ul>

          <Link
            href="https://getrecon.xyz/pro"
            className="m-0 flex flex-row items-center justify-center p-0 text-center"
            target="_blank"
            rel="noopener noreferrer"
          >
            <AppButton variant="primary" size="lg">
              Learn More about Recon Pro
            </AppButton>
          </Link>
          <div className="mt-[64px]"></div>

          <div className="landing-info-block my-[22px] hidden items-center gap-[164px] rounded-[17px] px-[51px] py-[33px] lg:flex">
            <div className="flex items-center">
              <Image
                src="/deploy-icon.svg"
                alt="Deploy"
                width={67}
                height={77}
                className="mr-[48px]"
              />
              <p className="max-w-[214px] text-[24px] font-thin leading-[24px] text-white">
                Helping these projects build more safely
              </p>
            </div>
            <div className="flex items-center gap-[106px]">
              <Link href="https://youtu.be/AT3fMhPDZFU" target="_blank">
                <Image
                  src="/centrifuge-logo.svg"
                  alt="Centrifuge logo"
                  width={126}
                  height={37}
                  className="mr-[48px]"
                />
              </Link>
              <Link href="https://youtu.be/3pvWq_zBauY" target="_blank">
                <Image
                  src="/badger-logo.svg"
                  alt="Badger logo"
                  width={137}
                  height={40}
                  className="mr-[48px]"
                />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

"use client";
import React, { useEffect, useState, useCallback } from "react";
import axios from "axios";

import type { Job } from "@/app/services/jobs.hooks";
import type { Organization } from "@/app/services/organization.hooks";
import config from "@/config";

// Import components we'll create
import { AdminHeader } from "./components/AdminHeader";
import { AdminStats } from "./components/AdminStats";
import { AdminSelectors } from "./components/AdminSelectors";
import { AdminActions } from "./components/AdminActions";
import { AdminOrgData } from "./components/AdminOrgData";
import { AdminJobsSection } from "./components/AdminJobsSection";

interface User {
  id: string;
  organizationId: string;
  username?: string;
}

export default function Admin() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);
  const [allOrgs, setAllOrgs] = useState<Organization[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);
  const [jobId, setJobId] = useState<string | null>(null);
  const [orgInvite, setOrgInvite] = useState<string | null>(null);
  const [displayOrgData, setDisplayOrgData] = useState<boolean>(false);
  const [displayQueuedJobs, setDisplayQueuedJobs] = useState<boolean>(false);
  const [orgData, setOrgData] = useState(null);
  const [allQueuedJobs, setAllQueuedJobs] = useState<Job[]>([]);
  const [runningJobCount, setRunningJobCount] = useState<number>(0);

  const billingStatus = ["UNPAID", "PAID", "REVOKED", "TRIAL"];
  const env =
    config?.github?.app?.installationUrl
      ?.split("https://github.com/apps/")[1]
      ?.split("/installations/new")[0] || "recon";
  const CACHE_KEY_USER = `${env}-users`;
  const CACHE_KEY_ORGS = `${env}-orgs`;
  const CACHE_EXPIRY = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

  const getAllOrgs = useCallback(async () => {
    try {
      const cached = localStorage.getItem(CACHE_KEY_ORGS);
      if (cached) {
        const { data, timestamp } = JSON.parse(cached);
        const isValid = Date.now() - timestamp < CACHE_EXPIRY;

        if (isValid) {
          setAllOrgs(data);
          return;
        }
      }

      const res = await axios.get("/api/admin/orgs");
      localStorage.setItem(
        CACHE_KEY_ORGS,
        JSON.stringify({
          data: res.data.data,
          timestamp: Date.now(),
        })
      );
      setAllOrgs(res.data.data);
    } catch (error) {
      console.error("Failed to fetch orgs:", error);
    }
  }, [CACHE_EXPIRY, CACHE_KEY_ORGS]);

  const fetchGithubUsername = async (userId: string) => {
    try {
      const {
        data: {
          data: { login },
        },
      } = await axios({
        method: "POST",
        url: "/api/admin/getGithubUsername",
        data: { userId },
      });
      return login;
    } catch (err) {
      return null;
    }
  };

  const getAllUsers = useCallback(async () => {
    try {
      const cached = localStorage.getItem(CACHE_KEY_USER);
      if (cached) {
        const { data, timestamp } = JSON.parse(cached);
        const isValid = Date.now() - timestamp < CACHE_EXPIRY;

        if (isValid) {
          setUsers(data);
          return;
        }
      }

      const res = await axios.get("/api/admin/users");
      const users = res.data.data;

      const usersWithUsernames = await Promise.all(
        users.map(async (user) => {
          const username = await fetchGithubUsername(user.id);
          return username ? { ...user, username } : user;
        })
      );

      // Cache results
      localStorage.setItem(
        CACHE_KEY_USER,
        JSON.stringify({
          data: usersWithUsernames,
          timestamp: Date.now(),
        })
      );

      setUsers(usersWithUsernames);
    } catch (error) {
      console.error("Failed to fetch users:", error);
    }
  }, [CACHE_EXPIRY, CACHE_KEY_USER]);

  const getQueuedJobs = useCallback(async () => {
    try {
      const res = await axios.get("/api/admin/job/queued");
      setAllQueuedJobs(res.data.data.data);
    } catch (err) {
      console.log("err getting the queued jobs", err);
    }
  }, []);

  const getOrgData = useCallback(async (orgId: string) => {
    const res = await axios({
      method: "POST",
      url: "/api/admin/orgs/info",
      data: {
        orgId: orgId,
      },
    });
    const {
      data: {
        data: { campaigns, jobs, recipes, recurringJobs },
      },
    } = res;
    setOrgData({ campaigns, jobs, recipes, recurringJobs });
  }, []);

  const getRunningJobCount = useCallback(async () => {
    try {
      const res = await axios.get("/api/admin/job/countrunning");
      setRunningJobCount(res.data.data.count);
    } catch (err) {
      console.log("err", err);
    }
  }, []);

  const reset = async () => {
    setSelectedOrg(null);
    setAllOrgs([]);
    setSelectedUser(null);
    setSelectedStatus("");
    await getAllOrgs();
    await getAllUsers();
  };

  const init = useCallback(async () => {
    try {
      const res = await axios.get("/api/admin");
      if (res.status === 200) {
        setIsSuperAdmin(true);
        await getAllOrgs();
        await getAllUsers();
        await getQueuedJobs();
        await getRunningJobCount();
        setSelectedOrg(null);
        setSelectedUser(null);
        setSelectedStatus("");
      }
    } catch (err) {
      console.log("Not Admin");
      window.location.href = "/dashboard"; // Force redirect if not admin
    }
    setIsLoading(false);
  }, [getAllOrgs, getAllUsers, getQueuedJobs, getRunningJobCount]);

  useEffect(() => {
    init();
  }, [init]);

  function handleOrgSelect(value: string): void {
    const org = allOrgs.find((org) => org.id === value);
    setSelectedOrg(org);
    getOrgData(org.id);
  }

  function handleUserSelect(value: string): void {
    const user = users.find((user) => user.id === value);
    setSelectedUser(user);
  }

  function statusHandler(value: string): void {
    if (!value) {
      alert("Please select a status");
      return;
    }
    if (selectedOrg) {
      setSelectedStatus(value);
    }
  }

  const clearCacheHandler = async () => {
    localStorage.removeItem(CACHE_KEY_ORGS);
    localStorage.removeItem(CACHE_KEY_USER);
    await reset();
  };

  async function orgStatusHandler() {
    try {
      await axios({
        method: "POST",
        url: "/api/admin/orgs/setstatus",
        data: {
          orgId: selectedOrg.id,
          status: selectedStatus,
        },
      });

      // Update item in the local storage:
      const data = JSON.parse(localStorage.getItem(CACHE_KEY_ORGS));
      const orgIndex = data.data.findIndex((org) => org.id === selectedOrg.id);
      data.data[orgIndex].billingStatus = selectedStatus;
      localStorage.setItem(CACHE_KEY_ORGS, JSON.stringify(data));

      setSelectedOrg(null);
      setSelectedStatus("");
      alert("Org status switched");
      await reset();
    } catch (err) {
      alert("Fail to switch org status");
    }
  }

  async function createOrgInviteHandler() {
    try {
      const { data } = await axios({
        method: "POST",
        url: "/api/admin/orgs/invite",
        data: {
          orgId: selectedOrg.id,
        },
      });
      setOrgInvite(data.data.code);
    } catch (err) {
      alert("Failed to create org invite");
    }
  }

  async function switchOrgHandler() {
    try {
      await axios({
        method: "POST",
        url: "/api/admin/orgs/switch",
        data: {
          userId: selectedUser.id,
          orgId: selectedOrg.id,
        },
      });
      setSelectedOrg(null);
      setSelectedUser(null);
      alert("Org switched");

      // Update item in the local storage:
      const dataOrgs = JSON.parse(localStorage.getItem(CACHE_KEY_ORGS));
      const orgIndex = dataOrgs.data.findIndex(
        (org) => org.id === selectedOrg.id
      );
      const dataUsers = JSON.parse(localStorage.getItem(CACHE_KEY_USER));
      const userIndex = dataUsers.data.findIndex(
        (user) => user.id === selectedUser.id
      );
      dataUsers.data[userIndex].organizationId = selectedOrg.id;
      localStorage.setItem(CACHE_KEY_ORGS, JSON.stringify(dataOrgs));
      localStorage.setItem(CACHE_KEY_USER, JSON.stringify(dataUsers));

      await reset();
    } catch (err) {
      alert("Failed to switch org");
    }
  }

  async function jobDeleteHandler() {
    try {
      await axios({
        method: "post",
        url: `/api/admin/job/delete`,
        data: {
          jobId: jobId,
        },
      });
      alert("job deleted");
      setJobId("");
    } catch (err) {
      console.log("err", err);
      alert(`Failed to delete job:\n ${err.response.data.message}`);
    }
  }

  async function orgDeleteHandler() {
    // try {
    //   await axios({
    //     method: "POST",
    //     url: `/api/admin/orgs/delete`,
    //     data: {
    //       jobId: jobId,
    //     },
    //   });
    // } catch (err) {
    //   console.log("err", err);
    //   alert(`Failed to delete job:\n ${err.response.data.message}`);
    // }
    alert("NOT implemented yet");
  }

  return (
    <div className="min-h-screen bg-back-neutral-secondary p-6">
      <div className="mx-auto max-w-7xl">
        <AdminHeader isLoading={isLoading} isSuperAdmin={isSuperAdmin} />

        {isSuperAdmin && (
          <>
            <AdminStats allOrgs={allOrgs} runningJobCount={runningJobCount} />

            <AdminSelectors
              allOrgs={allOrgs}
              users={users}
              selectedOrg={selectedOrg}
              selectedUser={selectedUser}
              onOrgSelect={handleOrgSelect}
              onUserSelect={handleUserSelect}
              onClearCache={clearCacheHandler}
            />

            <AdminActions
              selectedOrg={selectedOrg}
              selectedUser={selectedUser}
              selectedStatus={selectedStatus}
              jobId={jobId}
              orgInvite={orgInvite}
              billingStatus={billingStatus}
              users={users}
              onStatusChange={setSelectedStatus}
              onOrgStatusUpdate={orgStatusHandler}
              onCreateOrgInvite={createOrgInviteHandler}
              onSwitchOrg={switchOrgHandler}
              onJobIdChange={setJobId}
              onDeleteJob={jobDeleteHandler}
              onDeleteOrg={orgDeleteHandler}
            />

            <AdminOrgData
              selectedOrg={selectedOrg}
              orgData={orgData}
              displayOrgData={displayOrgData}
              onToggleDisplay={() => setDisplayOrgData(!displayOrgData)}
            />

            <AdminJobsSection
              queuedJobs={allQueuedJobs}
              displayQueuedJobs={displayQueuedJobs}
              onToggleQueuedJobs={() =>
                setDisplayQueuedJobs(!displayQueuedJobs)
              }
              onRefreshQueuedJobs={getQueuedJobs}
            />
          </>
        )}
      </div>
    </div>
  );
}

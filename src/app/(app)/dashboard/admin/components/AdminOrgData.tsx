"use client";
import React from "react";
import { AppCode } from "@/app/components/app-code";
import { Title2Strong, Body2, Body3 } from "@/app/components/app-typography";
import type { Organization } from "@/app/services/organization.hooks";

interface OrgData {
  campaigns: any[];
  jobs: any[];
  recipes: any[];
  recurringJobs: any[];
}

interface AdminOrgDataProps {
  selectedOrg: Organization | null;
  orgData: OrgData | null;
  displayOrgData: boolean;
  onToggleDisplay: () => void;
}

interface DataSectionProps {
  title: string;
  data: any[];
  icon: string;
}

const DataSection: React.FC<DataSectionProps> = ({ title, data, icon }) => {
  if (data.length === 0) {
    return (
      <div className="rounded-lg border border-stroke-neutral-decorative bg-back-neutral-secondary p-4">
        <div className="mb-2 flex items-center gap-2">
          <span className="text-lg">{icon}</span>
          <Body3 color="primary" className="font-medium">
            {title} (0)
          </Body3>
        </div>
        <Body3 color="tertiary" className="py-4 text-center">
          No {title.toLowerCase()} found
        </Body3>
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-stroke-neutral-decorative bg-back-neutral-secondary p-4">
      <div className="mb-4 flex items-center gap-2">
        <span className="text-lg">{icon}</span>
        <Body3 color="primary" className="font-medium">
          {title} ({data.length})
        </Body3>
      </div>
      <div className="max-h-96 space-y-3 overflow-y-auto">
        {data.map((item, index) => (
          <div
            key={item.id || index}
            className="rounded-lg border border-stroke-neutral-decorative bg-back-neutral-primary p-3"
          >
            <AppCode
              language="json"
              code={JSON.stringify(item, null, 2)}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export const AdminOrgData: React.FC<AdminOrgDataProps> = ({
  selectedOrg,
  orgData,
  displayOrgData,
  onToggleDisplay,
}) => {
  if (!selectedOrg) {
    return (
      <div className="mb-8">
        <div className="rounded-xl border border-stroke-neutral-decorative bg-back-neutral-tertiary p-6">
          <div className="mb-2 flex items-center gap-3">
            <span className="text-2xl">📊</span>
            <Title2Strong color="primary">Organization Data</Title2Strong>
          </div>
          <Body2 color="secondary">
            Select an organization to view detailed data
          </Body2>
        </div>
      </div>
    );
  }

  if (!orgData) {
    return (
      <div className="mb-8">
        <div className="rounded-xl border border-stroke-neutral-decorative bg-back-neutral-tertiary p-6">
          <div className="mb-2 flex items-center gap-3">
            <span className="text-2xl">📊</span>
            <Title2Strong color="primary">Organization Data</Title2Strong>
          </div>
          <div className="flex items-center gap-3">
            <div className="bg-accent-primary/20 size-4 animate-spin rounded-full border-2 border-accent-primary border-t-transparent"></div>
            <Body2 color="secondary">Loading organization data...</Body2>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-8">
      <div className="overflow-hidden rounded-xl border border-stroke-neutral-decorative bg-back-neutral-tertiary">
        {/* Header */}
        <div
          className="cursor-pointer p-6 transition-colors duration-200 hover:bg-back-neutral-secondary"
          onClick={onToggleDisplay}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="text-2xl">📊</span>
              <Title2Strong color="primary">
                Organization Data - {selectedOrg.name}
              </Title2Strong>
            </div>
            <div className="flex items-center gap-3">
              <Body3 color="secondary" className="text-xs">
                Click to {displayOrgData ? "collapse" : "expand"}
              </Body3>
              <span className="text-lg transition-transform duration-200">
                {displayOrgData ? "▲" : "▼"}
              </span>
            </div>
          </div>
          <Body2 color="secondary" className="mt-2">
            Jobs: {orgData.jobs.length} • Recipes: {orgData.recipes.length} • 
            Campaigns: {orgData.campaigns.length} • Recurring: {orgData.recurringJobs.length}
          </Body2>
        </div>

        {/* Expandable Content */}
        {displayOrgData && (
          <div className="border-t border-stroke-neutral-decorative p-6 pt-0">
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <DataSection
                title="Jobs"
                data={orgData.jobs}
                icon="🚀"
              />
              <DataSection
                title="Recipes"
                data={orgData.recipes}
                icon="📝"
              />
              <DataSection
                title="Campaigns"
                data={orgData.campaigns}
                icon="📢"
              />
              <DataSection
                title="Recurring Jobs"
                data={orgData.recurringJobs}
                icon="🔄"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

"use client";
import React from "react";
import { AppButton } from "@/app/components/app-button";
import { AppCode } from "@/app/components/app-code";
import { Title2Strong, Body2, Body3 } from "@/app/components/app-typography";
import type { Job } from "@/app/services/jobs.hooks";

interface AdminJobsSectionProps {
  queuedJobs: Job[];
  displayQueuedJobs: boolean;
  onToggleQueuedJobs: () => void;
  onRefreshQueuedJobs: () => void;
}

interface JobCardProps {
  job: Job;
}

const JobCard: React.FC<JobCardProps> = ({ job }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "QUEUED":
        return "text-accent-primary bg-accent-primary/10 border-accent-primary/20";
      case "RUNNING":
        return "text-status-success bg-status-success/10 border-status-success/20";
      case "SUCCESS":
        return "text-status-success bg-status-success/10 border-status-success/20";
      case "ERROR":
        return "text-status-error bg-status-error/10 border-status-error/20";
      case "STOPPED":
        return "text-fore-neutral-secondary bg-back-neutral-secondary border-stroke-neutral-decorative";
      default:
        return "text-fore-neutral-secondary bg-back-neutral-secondary border-stroke-neutral-decorative";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "QUEUED":
        return "⏳";
      case "RUNNING":
        return "🔄";
      case "SUCCESS":
        return "✅";
      case "ERROR":
        return "❌";
      case "STOPPED":
        return "⏹️";
      default:
        return "❓";
    }
  };

  return (
    <div className="rounded-lg border border-stroke-neutral-decorative bg-back-neutral-primary p-4 transition-all duration-200 hover:shadow-md">
      <div className="mb-3 flex items-start justify-between">
        <div className="min-w-0 flex-1">
          <div className="mb-2 flex items-center gap-2">
            <Body3 color="primary" className="truncate font-medium">
              {job.label || `Job ${job.id.slice(0, 8)}...`}
            </Body3>
            <div
              className={`rounded-full border px-2 py-1 text-xs font-medium ${getStatusColor(
                job.status
              )}`}
            >
              <span className="mr-1">{getStatusIcon(job.status)}</span>
              {job.status}
            </div>
          </div>
          
          <div className="space-y-1">
            <Body3 color="secondary" className="text-xs">
              ID: {job.id}
            </Body3>
            {job.organizationId && (
              <Body3 color="secondary" className="text-xs">
                Org: {job.organizationId.slice(0, 12)}...
              </Body3>
            )}
            {job.createdAt && (
              <Body3 color="secondary" className="text-xs">
                Created: {new Date(job.createdAt).toLocaleString()}
              </Body3>
            )}
          </div>
        </div>
      </div>

      {/* Job Details */}
      <div className="mt-3 border-t border-stroke-neutral-decorative pt-3">
        <details className="group">
          <summary className="cursor-pointer list-none">
            <div className="flex items-center justify-between">
              <Body3 color="secondary" className="text-xs">
                View Details
              </Body3>
              <span className="text-xs transition-transform duration-200 group-open:rotate-180">
                ▼
              </span>
            </div>
          </summary>
          <div className="mt-3">
            <AppCode
              language="json"
              code={JSON.stringify(job, null, 2)}
            />
          </div>
        </details>
      </div>
    </div>
  );
};

export const AdminJobsSection: React.FC<AdminJobsSectionProps> = ({
  queuedJobs,
  displayQueuedJobs,
  onToggleQueuedJobs,
  onRefreshQueuedJobs,
}) => {
  return (
    <div className="mb-8">
      <div className="overflow-hidden rounded-xl border border-stroke-neutral-decorative bg-back-neutral-tertiary">
        {/* Header */}
        <div className="p-6">
          <div className="mb-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="text-2xl">🚀</span>
              <Title2Strong color="primary">
                Queued Jobs ({queuedJobs.length})
              </Title2Strong>
            </div>
            <div className="flex items-center gap-3">
              <AppButton
                variant="outline"
                size="sm"
                onClick={onRefreshQueuedJobs}
                leftIcon={<span>🔄</span>}
              >
                Refresh
              </AppButton>
              <AppButton
                variant="secondary"
                size="sm"
                onClick={onToggleQueuedJobs}
              >
                {displayQueuedJobs ? "Hide Jobs" : "Show Jobs"}
              </AppButton>
            </div>
          </div>
          
          <Body2 color="secondary">
            Monitor and manage jobs currently in the system queue
          </Body2>
        </div>

        {/* Jobs List */}
        {displayQueuedJobs && (
          <div className="border-t border-stroke-neutral-decorative p-6 pt-0">
            {queuedJobs.length === 0 ? (
              <div className="py-12 text-center">
                <span className="mb-4 block text-4xl">📭</span>
                <Body2 color="secondary">No queued jobs found</Body2>
                <Body3 color="tertiary" className="mt-2">
                  All jobs are either completed or not yet submitted
                </Body3>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 xl:grid-cols-3">
                {queuedJobs.map((job) => (
                  <JobCard key={job.id} job={job} />
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

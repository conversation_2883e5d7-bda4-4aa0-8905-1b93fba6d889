"use client";
import React from "react";
import { Heading, Body2 } from "@/app/components/app-typography";

interface AdminHeaderProps {
  isLoading: boolean;
  isSuperAdmin: boolean;
}

export const AdminHeader: React.FC<AdminHeaderProps> = ({
  isLoading,
  isSuperAdmin,
}) => {
  if (isLoading) {
    return (
      <div className="mb-8">
        <div className="flex items-center justify-center p-8">
          <div className="flex animate-pulse items-center gap-3">
            <div className="bg-accent-primary/20 size-6 animate-spin rounded-full border-2 border-accent-primary border-t-transparent"></div>
            <Body2 color="secondary">Loading admin panel...</Body2>
          </div>
        </div>
      </div>
    );
  }

  if (!isSuperAdmin) {
    return (
      <div className="mb-8">
        <div className="bg-status-error/10 border-status-error/20 rounded-xl border p-6 text-center">
          <div className="mb-2">
            <span className="text-2xl">🚫</span>
          </div>
          <Heading color="primary" className="mb-2">
            Access Denied
          </Heading>
          <Body2 color="secondary">
            You don't have permission to access the admin panel.
          </Body2>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-8">
      <div className="rounded-xl border border-stroke-neutral-decorative bg-back-neutral-tertiary p-6">
        <div className="mb-2 flex items-center gap-3">
          <span className="text-2xl">⚡</span>
          <Heading color="primary">Admin Dashboard</Heading>
        </div>
        <Body2 color="secondary">
          Manage organizations, users, and system operations
        </Body2>
      </div>
    </div>
  );
};

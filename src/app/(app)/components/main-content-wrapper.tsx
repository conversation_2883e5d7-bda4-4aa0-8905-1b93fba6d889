"use client";

import React, { useEffect } from "react";

export const MainContentWrapper = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []); // Empty dependency array ensures it runs only once on mount

  return (
    <div className="min-h-screen grow bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <div className="mx-auto max-w-7xl px-6 py-12">{children}</div>
    </div>
  );
};

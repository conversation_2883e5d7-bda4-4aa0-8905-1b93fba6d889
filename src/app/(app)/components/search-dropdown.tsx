import { useEffect, useMemo, useRef, useState } from "react";
import { FiChevronDown, FiChevronUp, FiSearch } from "react-icons/fi";

import { cn } from "../../helpers/cn";
import { Body3 } from "@/app/components/app-typography";

interface Item {
  label: string;
  value: string | number;
}

interface SearchDropdownProps {
  items: Item[];
  onChange: (value: string | number) => void;
  searchPlaceholder: string;
  className?: string;
  disabled?: boolean;
  error?: string;
  label?: string;
}

//TODO - add keyboard support

export const SearchDropdown = ({
  items,
  onChange,
  searchPlaceholder,
  className = "",
  disabled = false,
  error,
  label,
}: SearchDropdownProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [search, setSearch] = useState("");

  useEffect(() => {
    if (!isOpen) return;
    const onClickOutside = (e: MouseEvent) => {
      if (ref.current && !ref.current.contains(e.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("click", onClickOutside, true);

    return () => {
      document.removeEventListener("click", onClickOutside, true);
    };
  }, [isOpen]);

  const toggleOpen = () => {
    if (disabled) return;
    setIsOpen((prev) => !prev);
    if (isOpen) setSearch("");
  };

  const selectItem = (item: Item) => {
    setSelectedItem(item);
    onChange(item.value);
    setIsOpen(false);
    setSearch("");
  };

  const filteredItems = useMemo(() => {
    if (!search) return items;
    return items.filter((item) => new RegExp(search, "i").test(item.label));
  }, [search, items]);

  const getButtonStyles = () => {
    const baseStyles =
      "h-[40px] rounded-[6px] px-4 w-full min-w-[200px] outline-none transition-all duration-200 font-bold text-[16px] leading-[1.375] flex items-center justify-between cursor-pointer";

    if (disabled) {
      return cn(
        baseStyles,
        "bg-transparent border border-stroke-neutral-decorative text-fore-neutral-quaternary opacity-40"
      );
    }

    if (error) {
      return cn(
        baseStyles,
        "bg-transparent border border-status-error text-fore-neutral-secondary hover:border-status-error/80 focus:border-status-error"
      );
    }

    return cn(
      baseStyles,
      "bg-transparent border border-stroke-neutral-decorative text-fore-neutral-secondary hover:border-fore-neutral-primary focus:border-fore-neutral-primary",
      isOpen && "border-accent-primary"
    );
  };

  const getSearchInputStyles = () => {
    return cn(
      "h-[40px] rounded-[6px] px-4 pl-10 w-full outline-none transition-all duration-200 font-bold text-[16px] leading-[1.375]",
      "bg-transparent border border-stroke-neutral-decorative text-fore-neutral-secondary placeholder:text-fore-neutral-quaternary hover:border-fore-neutral-primary focus:border-accent-primary"
    );
  };

  return (
    <div className={cn("relative w-full", className)}>
      {label && (
        <label className="mb-[3px] block text-[15px] leading-[18px] text-fore-neutral-secondary">
          {label}
        </label>
      )}

      <div ref={ref} className="relative">
        <button
          type="button"
          className={getButtonStyles()}
          onClick={toggleOpen}
          disabled={disabled}
        >
          <span className="truncate text-left">
            {selectedItem?.label || searchPlaceholder}
          </span>
          {isOpen ? (
            <FiChevronUp className="size-4 shrink-0" />
          ) : (
            <FiChevronDown className="size-4 shrink-0" />
          )}
        </button>

        {isOpen && (
          <div className="absolute inset-x-0 top-full z-50 mt-1 rounded-[6px] border border-stroke-neutral-decorative bg-back-neutral-primary shadow-lg">
            {/* Search Input */}
            <div className="relative p-2">
              <FiSearch className="absolute left-5 top-1/2 size-4 -translate-y-1/2 text-fore-neutral-quaternary" />
              <input
                type="text"
                className={getSearchInputStyles()}
                placeholder={searchPlaceholder}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                autoFocus
              />
            </div>

            {/* Items List */}
            <div className="max-h-48 overflow-y-auto">
              {filteredItems.length > 0 ? (
                filteredItems.map((item) => (
                  <button
                    key={item.value}
                    type="button"
                    className={cn(
                      "w-full px-4 py-2 text-left transition-colors duration-200 text-[15px] font-medium text-fore-neutral-primary hover:bg-back-neutral-secondary",
                      selectedItem?.value === item.value &&
                        "bg-accent-primary/10 text-accent-primary"
                    )}
                    onClick={() => selectItem(item)}
                  >
                    {item.label}
                  </button>
                ))
              ) : (
                <Body3 className="px-4 py-2 text-fore-neutral-quaternary">
                  No items found
                </Body3>
              )}
            </div>
          </div>
        )}
      </div>

      {error && (
        <Body3 className="mt-[3px] block h-[14px] text-status-error">
          {error}
        </Body3>
      )}
    </div>
  );
};

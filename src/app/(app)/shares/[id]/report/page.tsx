"use client";
import { use, useEffect, useState } from "react";
import axios from "axios";

import { AppButton } from "@/app/components/app-button";
import { H2 } from "@/app/components/app-typography";
import { downloadFile } from "@/app/(app)/tools/mdReportHelper";
import { generateJobMD, processLogs } from "@recon-fuzz/log-parser";
import type { Job } from "@/app/services/jobs.hooks";
import type { Fuzzer, FuzzingResults } from "@recon-fuzz/log-parser";
import Link from "next/link";
import { AppLogo } from "@/app/components/app-logo";
import JobReport from "@/app/components/JobReport/JobReport";
import LogComponent from "@/app/(app)/tools/logs-parser/logs-parser";
import type { ENV_TYPE } from "@/app/app.constants";
import { AppHeader } from "@/app/(app)/components/app-header";
import { FiArrowLeft, FiDownload } from "react-icons/fi";

export default function ShareReport({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = use(params);
  const [jobData, setJobData] = useState<Job | null>(null);
  const [md, setMd] = useState("");
  const [jobStats, setJobStats] = useState<FuzzingResults | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function getJobInfo() {
      if (!id) {
        return;
      }
      try {
        const res = await axios.get(`/api/shares/${id}`);
        setJobData(res?.data?.data?.job);
        setIsLoading(false);
      } catch (e) {
        console.log(e);
        setIsLoading(false);
      }
    }
    getJobInfo();
  }, [id]);

  useEffect(() => {
    async function getLogs() {
      const jobLogsRaw = await axios({
        method: "POST",
        url: `/api/fetchLogs`,
        data: {
          logsUrl: jobData?.logsUrl,
        },
      });
      const data = processLogs(jobLogsRaw.data, jobData?.fuzzer as Fuzzer);
      const jobStats = data;
      setJobStats(jobStats);
      const md = generateJobMD(
        jobData?.fuzzer as Fuzzer,
        jobLogsRaw.data,
        jobData?.label || `${jobData?.orgName}/${jobData?.repoName}`
      );
      setMd(md);
      setIsLoading(false);
    }
    getLogs();
  }, [jobData]);

  return (
    <div className="min-h-screen grow overflow-y-auto bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <AppHeader skipUser />
      {isLoading === false ? (
        <div className="p-10">
          <H2 className="mb-2" color="primary">
            Share report for {jobData?.orgName}/{jobData?.repoName}/
            {jobData?.ref}
          </H2>
          <div className="mb-4 flex flex-row justify-between">
            {md ? (
              <AppButton
                onClick={() => downloadFile(md, jobData.repoName)}
                leftIcon={<FiDownload size={18} />}
              >
                Download report
              </AppButton>
            ) : (
              ""
            )}
            <Link href={`/shares/${id}`}>
              <AppButton leftIcon={<FiArrowLeft size={18} />}>
                Back to Run Results
              </AppButton>
            </Link>
          </div>
          {md && jobData && jobStats ? (
            <div className="grid grid-cols-1 items-start gap-4 lg:grid-cols-2">
              <LogComponent
                fuzzer={jobData?.fuzzer as ENV_TYPE}
                logs={jobData.brokenProperties.join("\n\n")}
                jobStatsForced={jobStats}
              />
              <JobReport
                fuzzer={jobData.fuzzer}
                jobStats={jobStats}
                showBrokenProp={true}
              />
            </div>
          ) : (
            ""
          )}
        </div>
      ) : (
        <H2 className="text-center" color="primary">
          Loading...
        </H2>
      )}
    </div>
  );
}

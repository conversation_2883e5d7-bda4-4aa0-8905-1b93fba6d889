import type { Job } from "@/app/services/jobs.hooks";

export const mockJobs: Job[] = [
  {
    id: "job-001",
    orgName: "recon-fuzz",
    repoName: "smart-contracts",
    ref: "main",
    fuzzer: "ECHIDNA",
    directory: "contracts/",
    taskArn: "arn:aws:ecs:us-east-1:123456789:task/cluster/task-001",
    corpusUrl: "https://s3.amazonaws.com/recon-corpus/job-001/corpus.tar.gz",
    coverageUrl: "https://s3.amazonaws.com/recon-coverage/job-001/coverage.html",
    logsUrl: "https://s3.amazonaws.com/recon-logs/job-001/logs.txt",
    status: "SUCCESS",
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-15T12:45:00Z",
    fuzzerArgs: {
      timeout: 3600,
      testLimit: 50000,
      shrinkLimit: 5000,
      seqLen: 100,
      contractAddr: "******************************************",
      deployer: "******************************************",
      sender: ["******************************************", "******************************************"],
      psender: 0.1,
      fprefix: "echidna_",
      format: "text",
      coverage: true,
      corpusDir: "corpus/",
    },
    label: "Token Contract Security Audit",
    metadata: {
      commit: "a1b2c3d4e5f6789012345678901234567890abcd",
      method: "manual",
      startedBy: "<EMAIL>",
    },
    testsDuration: "2h 15m 30s",
    testsCoverage: 87.5,
    testsFailed: 3,
    testsPassed: 47,
    numberOfTests: 50,
    brokenProperties: [
      {
        brokenProperty: "echidna_balance_never_decreases",
        traces: `Call sequence:
1. transfer(******************************************, 1000)
2. approve(******************************************, 500)
3. transferFrom(******************************************, ******************************************, 600)

Event sequence:
- Transfer(******************************************, ******************************************, 1000)
- Approval(******************************************, ******************************************, 500)
- Transfer(******************************************, ******************************************, 600)

Property violated: Balance should never decrease without proper authorization`,
      },
      {
        brokenProperty: "echidna_total_supply_constant",
        traces: `Call sequence:
1. mint(******************************************, 1000)
2. burn(500)

Event sequence:
- Transfer(******************************************, ******************************************, 1000)
- Transfer(******************************************, ******************************************, 500)

Property violated: Total supply changed unexpectedly`,
      },
      {
        brokenProperty: "echidna_no_overflow",
        traces: `Call sequence:
1. transfer(******************************************, 115792089237316195423570985008687907853269984665640564039457584007913129639935)

Event sequence:
- Arithmetic overflow detected in balance calculation

Property violated: Integer overflow in balance computation`,
      },
    ],
    progress: 100,
    eta: null,
  },
  {
    id: "job-002",
    orgName: "defi-protocol",
    repoName: "lending-pool",
    ref: "develop",
    fuzzer: "MEDUSA",
    directory: "src/",
    taskArn: "arn:aws:ecs:us-east-1:123456789:task/cluster/task-002",
    corpusUrl: "https://s3.amazonaws.com/recon-corpus/job-002/corpus.tar.gz",
    coverageUrl: "https://s3.amazonaws.com/recon-coverage/job-002/coverage.html",
    logsUrl: "https://s3.amazonaws.com/recon-logs/job-002/logs.txt",
    status: "RUNNING",
    createdAt: "2024-01-16T08:15:00Z",
    updatedAt: "2024-01-16T10:30:00Z",
    fuzzerArgs: {
      timeout: 7200,
      testLimit: 100000,
      workers: 4,
      deploymentOrder: ["LendingPool", "PriceOracle", "InterestRateModel"],
      compilationTarget: "src/LendingPool.sol:LendingPool",
    },
    label: "Lending Pool Invariants",
    metadata: {
      commit: "b2c3d4e5f6789012345678901234567890abcdef",
      method: "automated",
      startedBy: "<EMAIL>",
    },
    testsDuration: "1h 45m 12s",
    testsCoverage: 72.3,
    testsFailed: 0,
    testsPassed: 23,
    numberOfTests: 23,
    brokenProperties: [],
    progress: 65,
    eta: "1h 20m",
  },
  {
    id: "job-003",
    orgName: "nft-marketplace",
    repoName: "marketplace-contracts",
    ref: "feature/royalties",
    fuzzer: "FOUNDRY",
    directory: "contracts/marketplace/",
    status: "ERROR",
    createdAt: "2024-01-16T14:20:00Z",
    updatedAt: "2024-01-16T14:25:00Z",
    fuzzerArgs: {
      fuzzRuns: 10000,
      maxTestRejects: 65536,
      seed: 12345,
      dictionary: "test/fuzz/dictionary.txt",
    },
    label: "NFT Royalty System",
    metadata: {
      commit: "c3d4e5f6789012345678901234567890abcdef12",
      method: "manual",
      startedBy: "<EMAIL>",
    },
    testsDuration: "5m 30s",
    testsCoverage: 0,
    testsFailed: 0,
    testsPassed: 0,
    numberOfTests: 0,
    brokenProperties: [],
    progress: 0,
    eta: null,
  },
  {
    id: "job-004",
    orgName: "governance-dao",
    repoName: "voting-system",
    ref: "main",
    fuzzer: "HALMOS",
    directory: "contracts/governance/",
    taskArn: "arn:aws:ecs:us-east-1:123456789:task/cluster/task-004",
    logsUrl: "https://s3.amazonaws.com/recon-logs/job-004/logs.txt",
    status: "SUCCESS",
    createdAt: "2024-01-14T16:45:00Z",
    updatedAt: "2024-01-14T18:30:00Z",
    fuzzerArgs: {
      function: "check_voting_power_consistency",
      loop: 10,
      unroll: 256,
      solver: "z3",
      timeout: 300,
    },
    label: "Governance Voting Logic",
    metadata: {
      commit: "d4e5f6789012345678901234567890abcdef1234",
      method: "automated",
      startedBy: "<EMAIL>",
    },
    testsDuration: "1h 45m 0s",
    testsCoverage: 95.2,
    testsFailed: 1,
    testsPassed: 9,
    numberOfTests: 10,
    brokenProperties: [
      {
        brokenProperty: "check_voting_power_consistency",
        traces: `Symbolic execution trace:
Initial state:
- voter1.balance = α (symbolic)
- voter2.balance = β (symbolic)
- totalSupply = α + β

Execution path:
1. delegate(voter1, voter2)
2. propose(proposal_id)
3. vote(proposal_id, true)

Constraint violation:
- Expected: votingPower(voter1) + votingPower(voter2) == totalSupply
- Actual: votingPower(voter1) + votingPower(voter2) > totalSupply
- Counterexample: α = 1000, β = 2000, delegation creates double counting`,
      },
    ],
    progress: 100,
    eta: null,
  },
  {
    id: "job-005",
    orgName: "bridge-protocol",
    repoName: "cross-chain-bridge",
    ref: "security-patch",
    fuzzer: "KONTROL",
    directory: "src/bridge/",
    status: "QUEUED",
    createdAt: "2024-01-16T15:00:00Z",
    updatedAt: "2024-01-16T15:00:00Z",
    fuzzerArgs: {
      test: "BridgeTest.test_bridge_invariants",
      verbose: 2,
      maxDepth: 1000,
      maxIterations: 1000,
    },
    metadata: {
      commit: "e5f6789012345678901234567890abcdef123456",
      method: "manual",
      startedBy: "<EMAIL>",
    },
    testsDuration: "0s",
    testsCoverage: 0,
    testsFailed: 0,
    testsPassed: 0,
    numberOfTests: 0,
    brokenProperties: [],
    progress: 0,
    eta: null,
  },
];

export const mockJobData: Job = mockJobs[0];

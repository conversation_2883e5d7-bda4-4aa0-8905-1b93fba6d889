# Mock Data Documentation

This directory contains comprehensive mock data for development and testing purposes.

## Available Mock Data

### 1. **Jobs Mock Data** (`jobs.mock.ts`)

- **`mockJobs`**: Array of 5 diverse job examples covering all major scenarios
- **`mockJobData`**: Single job object for individual job pages
- Includes jobs with different statuses: S<PERSON>CE<PERSON>, R<PERSON><PERSON><PERSON>, ERROR, QUEUED
- Covers all fuzzers: ECHIDNA, MEDUSA, FOUNDRY, HALMOS, KONTROL
- Contains realistic broken properties with detailed traces

### 2. **Shares Mock Data** (`shares.mock.ts`)

- **`mockShareJobData`**: Comprehensive shared job data
- Includes 5 detailed broken properties with realistic vulnerability scenarios
- Features complex traces showing reentrancy, access control, and fee calculation issues

### 3. **Recipes Mock Data** (`recipes.mock.ts`)

- **`mockRecipes`**: Array of 8 diverse recipe configurations
- Covers all fuzzer types: ECHIDNA, MEDUSA, FOUNDRY, HALMOS
- Includes realistic DeFi, NFT, governance, and bridge protocol scenarios
- Contains proper fuzzer arguments and contract preparation configurations

### 4. **Job Logs Mock Data** (`logs.mock.ts`)

- **`mockLogsByFuzzer`**: Object mapping fuzzer types to their specific log formats
- **`mockEchidnaLogs`**: Realistic Echidna fuzzing campaign logs with test results
- **`mockMedusaLogs`**: Medusa fuzzing logs with worker pool and counterexamples
- **`mockFoundryLogs`**: Foundry test execution logs with gas usage and traces
- **`mockHalmosLogs`**: Halmos symbolic execution logs with path exploration
- **`mockKontrolLogs`**: Kontrol formal verification logs with claim proofs
- **`mockJobLogs`**: Default logs (Echidna format) for general use

### 5. **Job Stats Mock Data** (`jobStats.mock.ts`)

Mock FuzzingResults data for testing job statistics and reports:

- **`mockJobStatsByFuzzer`**: Object mapping fuzzer types to their specific job stats
- **`mockEchidnaJobStats`**: Echidna fuzzing results with coverage, tests, and broken properties
- **`mockMedusaJobStats`**: Medusa fuzzing results with test outcomes and traces
- **`mockFoundryJobStats`**: Foundry test results with gas usage and fuzz run statistics
- **`mockHalmosJobStats`**: Halmos symbolic execution results with claim proofs
- **`mockKontrolJobStats`**: Kontrol formal verification results with proof outcomes
- **`mockJobStats`**: Default job stats (Echidna format) for general use

## Usage

Import mock data from the central index file:

```typescript
import {
  mockJobs,
  mockJobData,
  mockShareJobData,
  mockRecipes,
  mockJobLogs,
  mockLogsByFuzzer,
  mockJobStats,
  mockJobStatsByFuzzer,
} from "@/app/mocks";
```

## Integration

Mock data is automatically used as fallback when:

- API endpoints are unavailable
- During development without backend connection
- For testing UI components with realistic data

## Mock Data Features

### **Comprehensive Coverage**

- ✅ All job statuses (SUCCESS, RUNNING, ERROR, QUEUED, STOPPED)
- ✅ All fuzzer types (ECHIDNA, MEDUSA, FOUNDRY, HALMOS, KONTROL)
- ✅ Realistic broken properties with detailed traces
- ✅ Progress tracking and ETA calculations
- ✅ Proper metadata and timestamps
- ✅ Recipe configurations for various protocol types
- ✅ Realistic job logs for all fuzzer types with proper formatting
- ✅ Fuzzer-specific log patterns and output structures

### **Realistic Scenarios**

- **Security Vulnerabilities**: Reentrancy, access control, overflow
- **DeFi-Specific Issues**: Fee calculations, withdrawal queues, vault integrity
- **Governance Problems**: Voting power inconsistencies, delegation issues
- **Bridge Security**: Cross-chain validation failures
- **Protocol Types**: Lending, AMM, NFT marketplaces, staking, options

### **Development Benefits**

- 🚀 **No Backend Dependency**: Work offline with realistic data
- 🎨 **UI Testing**: Test all states and edge cases
- 🐛 **Error Scenarios**: Test error handling and edge cases
- 📊 **Performance**: Fast loading without API calls
- 🔧 **Recipe Testing**: Test campaign creation with various recipe types

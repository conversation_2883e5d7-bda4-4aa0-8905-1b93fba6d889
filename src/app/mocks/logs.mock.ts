// Mock job logs data for different fuzzer types

export const mockEchidnaLogs = `
Analyzing contract: /tmp/contracts/Vault.sol:Vault
Loaded total of 15 tests
Starting fuzzing campaign...

echidna_test_deposit_withdraw: passed! 🎉
echidna_test_balance_consistency: passed! 🎉
echidna_test_total_supply_invariant: passed! 🎉
echidna_test_no_reentrancy: FAILED!💥  
  Call sequence:
    1. deposit(1000000000000000000)
    2. withdraw(500000000000000000)
    3. emergencyWithdraw()

echidna_test_access_control: passed! 🎉
echidna_test_fee_calculation: FAILED!💥
  Call sequence:
    1. setFeeRate(10000)
    2. deposit(1000000000000000000)
    3. withdraw(1000000000000000000)

echidna_test_pause_functionality: passed! 🎉
echidna_test_upgrade_authorization: passed! 🎉
echidna_test_slippage_protection: passed! 🎉
echidna_test_oracle_price_bounds: passed! 🎉

Campaign completed!
Tests run: 50000
Passed: 8
Failed: 2
Coverage: 87.5%
Duration: 3600s
Shrinking completed in 45s
`;

export const mockMedusaLogs = `
[INFO] Medusa v0.1.3 starting...
[INFO] Compilation successful
[INFO] Starting fuzzing campaign for NFTMarketplace
[INFO] Worker pool initialized with 4 workers

[INFO] Test: test_listing_price_consistency - PASSED ✓
[INFO] Test: test_royalty_calculation - PASSED ✓
[INFO] Test: test_marketplace_fee_collection - FAILED ✗
  Counterexample found:
    createListing(tokenId=123, price=1000000000000000000)
    updateListingPrice(tokenId=123, newPrice=0)
    purchaseItem(tokenId=123)

[INFO] Test: test_ownership_transfer - PASSED ✓
[INFO] Test: test_auction_bidding - FAILED ✗
  Counterexample found:
    startAuction(tokenId=456, startingBid=1000000000000000000, duration=3600)
    placeBid(tokenId=456, bidAmount=999999999999999999)
    endAuction(tokenId=456)

[INFO] Test: test_escrow_functionality - PASSED ✓
[INFO] Test: test_metadata_integrity - PASSED ✓
[INFO] Test: test_batch_operations - PASSED ✓

[INFO] Fuzzing campaign completed
[INFO] Total execution time: 2847 seconds
[INFO] Tests executed: 75000
[INFO] Passed: 6
[INFO] Failed: 2
[INFO] Code coverage: 92.3%
[INFO] Corpus saved to: /tmp/corpus/
`;

export const mockFoundryLogs = `
[⠊] Compiling...
[⠒] Compiling 45 files with 0.8.19
[⠢] Solc 0.8.19 finished in 3.21s
Compiler run successful!

Running 12 tests for test/GovernorTest.t.sol:GovernorTest
[PASS] test_proposal_creation() (gas: 234567)
[PASS] test_voting_power_calculation() (gas: 187432)
[FAIL] test_quorum_threshold() (gas: 298765)
  Error: Assertion failed
    Expected: 1000000000000000000
    Actual: 999999999999999999
  Traces:
    [298765] GovernorTest::test_quorum_threshold()
    ├─ [234567] Governor::propose(targets, values, calldatas, description)
    ├─ [123456] Governor::castVote(proposalId, support)
    └─ [87654] Governor::execute(proposalId)

[PASS] test_timelock_delay() (gas: 156789)
[FAIL] test_delegation_overflow() (gas: 345678)
  Error: Arithmetic overflow
  Traces:
    [345678] GovernorTest::test_delegation_overflow()
    ├─ [234567] GovernanceToken::delegate(delegatee)
    ├─ [123456] GovernanceToken::getVotes(account)
    └─ [87654] Governor::propose(targets, values, calldatas, description)

[PASS] test_proposal_execution() (gas: 278901)
[PASS] test_vote_counting() (gas: 198765)
[PASS] test_proposal_cancellation() (gas: 167890)

Test result: FAILED. 8 passed; 2 failed; finished in 45.67s
`;

export const mockHalmosLogs = `
Halmos 0.1.9 starting symbolic execution...
Compiling contracts...
✓ Compilation successful

Analyzing contract: CrossChainBridge
Running symbolic execution on 8 functions...

✓ test_bridge_deposit_consistency() - PASSED
  Symbolic execution completed in 12.3s
  Paths explored: 156

✓ test_withdrawal_authorization() - PASSED  
  Symbolic execution completed in 8.7s
  Paths explored: 89

✗ test_cross_chain_validation() - FAILED
  Counterexample found:
    sourceChainId: 1
    targetChainId: 137
    amount: 1000000000000000000
    nonce: 0
  Symbolic execution completed in 15.2s
  Paths explored: 234

✓ test_fee_distribution() - PASSED
  Symbolic execution completed in 6.4s
  Paths explored: 67

✗ test_replay_protection() - FAILED
  Counterexample found:
    messageHash: 0x1234567890abcdef...
    signature: 0xabcdef1234567890...
    nonce: 42
  Symbolic execution completed in 18.9s
  Paths explored: 312

✓ test_pause_mechanism() - PASSED
  Symbolic execution completed in 4.2s
  Paths explored: 23

✓ test_admin_functions() - PASSED
  Symbolic execution completed in 7.8s
  Paths explored: 45

✓ test_emergency_withdrawal() - PASSED
  Symbolic execution completed in 9.1s
  Paths explored: 78

Summary:
Total functions analyzed: 8
Passed: 6
Failed: 2
Total execution time: 82.6s
Total paths explored: 1004
`;

export const mockKontrolLogs = `
Kontrol v0.1.2 - Formal Verification Engine
Initializing K framework...
Loading contract bytecode...

Verifying contract: StakingRewards
Specification file: StakingRewards.k

Claim 1: stake_increases_balance
  Status: PROVED ✓
  Verification time: 23.4s
  
Claim 2: unstake_decreases_balance  
  Status: PROVED ✓
  Verification time: 18.7s

Claim 3: reward_calculation_accuracy
  Status: FAILED ✗
  Counterexample:
    Initial state: totalSupply = 1000000000000000000
    Action: stake(amount = 1)
    Final state: rewards miscalculated by 1 wei
  Verification time: 45.2s

Claim 4: no_reward_before_staking
  Status: PROVED ✓
  Verification time: 12.1s

Claim 5: withdrawal_authorization
  Status: FAILED ✗
  Counterexample:
    User A stakes 1000 tokens
    User B can withdraw User A's stake
  Verification time: 67.8s

Claim 6: emergency_pause_effectiveness
  Status: PROVED ✓
  Verification time: 31.5s

Verification Summary:
Total claims: 6
Proved: 4
Failed: 2
Total verification time: 198.7s
`;

// Map of fuzzer types to their corresponding mock logs
export const mockLogsByFuzzer = {
  ECHIDNA: mockEchidnaLogs,
  MEDUSA: mockMedusaLogs,
  FOUNDRY: mockFoundryLogs,
  HALMOS: mockHalmosLogs,
  KONTROL: mockKontrolLogs,
};

// Default mock logs (Echidna format)
export const mockJobLogs = mockEchidnaLogs;

import type { Recipe } from "@/app/services/recipes.hook";

export const mockRecipes: Recipe[] = [
  {
    id: "recipe-1",
    displayName: "DeFi Protocol Security Suite",
    orgName: "defi-protocol",
    repoName: "core-contracts",
    ref: "main",
    fuzzer: "ECHIDNA",
    fuzzerArgs: {
      target: "contracts/",
      prepareContracts: [
        {
          target: "contracts/Vault.sol",
          replacement: "contracts/test/VaultTest.sol",
          targetContract: "Vault"
        },
        {
          target: "contracts/Token.sol", 
          replacement: "contracts/test/TokenTest.sol",
          targetContract: "ERC20Token"
        }
      ]
    }
  },
  {
    id: "recipe-2", 
    displayName: "NFT Marketplace Fuzzing",
    orgName: "nft-marketplace",
    repoName: "marketplace-contracts",
    ref: "develop",
    fuzzer: "MEDUSA",
    fuzzerArgs: {
      target: "src/",
      prepareContracts: [
        {
          target: "src/Marketplace.sol",
          replacement: "test/MarketplaceTest.sol", 
          targetContract: "NFTMarketplace"
        }
      ]
    }
  },
  {
    id: "recipe-3",
    displayName: "Governance Token Analysis",
    orgName: "dao-governance",
    repoName: "governance-contracts", 
    ref: "main",
    fuzzer: "FOUNDRY",
    fuzzerArgs: {
      target: "contracts/governance/",
      prepareContracts: [
        {
          target: "contracts/governance/Governor.sol",
          replacement: "test/GovernorTest.sol",
          targetContract: "GovernorContract"
        },
        {
          target: "contracts/governance/Timelock.sol",
          replacement: "test/TimelockTest.sol", 
          targetContract: "TimelockController"
        }
      ]
    }
  },
  {
    id: "recipe-4",
    displayName: "Bridge Security Testing",
    orgName: "cross-chain",
    repoName: "bridge-protocol",
    ref: "security-audit",
    fuzzer: "HALMOS",
    fuzzerArgs: {
      target: "contracts/bridge/",
      prepareContracts: [
        {
          target: "contracts/bridge/Bridge.sol",
          replacement: "test/BridgeTest.sol",
          targetContract: "CrossChainBridge"
        }
      ]
    }
  },
  {
    id: "recipe-5",
    displayName: "Lending Protocol Invariants",
    orgName: "lending-protocol",
    repoName: "lending-core",
    ref: "v2.0",
    fuzzer: "ECHIDNA", 
    fuzzerArgs: {
      target: "src/core/",
      prepareContracts: [
        {
          target: "src/core/LendingPool.sol",
          replacement: "test/LendingPoolTest.sol",
          targetContract: "LendingPool"
        },
        {
          target: "src/core/InterestRate.sol",
          replacement: "test/InterestRateTest.sol",
          targetContract: "InterestRateModel"
        }
      ]
    }
  },
  {
    id: "recipe-6",
    displayName: "AMM Liquidity Testing",
    orgName: "amm-dex",
    repoName: "amm-contracts",
    ref: "main",
    fuzzer: "MEDUSA",
    fuzzerArgs: {
      target: "contracts/core/",
      prepareContracts: [
        {
          target: "contracts/core/UniswapV2Pair.sol",
          replacement: "test/PairTest.sol",
          targetContract: "UniswapV2Pair"
        }
      ]
    }
  },
  {
    id: "recipe-7",
    displayName: "Staking Rewards Security",
    orgName: "staking-protocol", 
    repoName: "staking-contracts",
    ref: "main",
    fuzzer: "FOUNDRY",
    fuzzerArgs: {
      target: "contracts/",
      prepareContracts: [
        {
          target: "contracts/StakingRewards.sol",
          replacement: "test/StakingRewardsTest.sol",
          targetContract: "StakingRewards"
        }
      ]
    }
  },
  {
    id: "recipe-8",
    displayName: "Options Protocol Testing",
    orgName: "options-protocol",
    repoName: "options-core",
    ref: "develop",
    fuzzer: "HALMOS",
    fuzzerArgs: {
      target: "src/options/",
      prepareContracts: [
        {
          target: "src/options/OptionsMarket.sol",
          replacement: "test/OptionsMarketTest.sol",
          targetContract: "OptionsMarket"
        }
      ]
    }
  }
];

import type { Job } from "@/app/services/jobs.hooks";

export const mockShareJobData: Job = {
  id: "shared-job-001",
  orgName: "public-audit",
  repoName: "defi-vault",
  ref: "audit-v2.1",
  fuzzer: "ECHIDNA",
  directory: "contracts/vault/",
  taskArn: "arn:aws:ecs:us-east-1:123456789:task/cluster/shared-task-001",
  corpusUrl: "https://s3.amazonaws.com/recon-corpus/shared-job-001/corpus.tar.gz",
  coverageUrl: "https://s3.amazonaws.com/recon-coverage/shared-job-001/coverage.html",
  logsUrl: "https://s3.amazonaws.com/recon-logs/shared-job-001/logs.txt",
  status: "SUCCESS",
  createdAt: "2024-01-10T09:00:00Z",
  updatedAt: "2024-01-10T14:30:00Z",
  fuzzerArgs: {
    timeout: 14400, // 4 hours
    testLimit: 200000,
    shrinkLimit: 10000,
    seqLen: 150,
    contractAddr: "0x742d35Cc6634C0532925a3b8D4C9db96C4b4d8b9",
    deployer: "0x0000000000000000000000000000000000000000",
    sender: [
      "******************************************",
      "******************************************",
      "******************************************"
    ],
    psender: 0.15,
    fprefix: "echidna_",
    format: "text",
    coverage: true,
    corpusDir: "corpus/",
    checkAsserts: true,
    estimateGas: true,
  },
  label: "DeFi Vault Security Analysis - Public Audit",
  metadata: {
    commit: "f6789012345678901234567890abcdef12345678",
    method: "automated",
    startedBy: "<EMAIL>",
  },
  testsDuration: "5h 30m 45s",
  testsCoverage: 92.8,
  testsFailed: 5,
  testsPassed: 195,
  numberOfTests: 200,
  brokenProperties: [
    {
      brokenProperty: "echidna_vault_balance_integrity",
      traces: `Call sequence that breaks vault balance integrity:
1. deposit(1000 ether) -> user deposits 1000 ETH
2. requestWithdrawal(800 ether) -> user requests withdrawal of 800 ETH
3. emergencyPause() -> admin pauses the contract
4. emergencyWithdraw() -> user bypasses normal withdrawal process

Event sequence:
- Deposit(user, 1000000000000000000000)
- WithdrawalRequested(user, 800000000000000000000, block.timestamp + 7 days)
- EmergencyPaused(admin, block.timestamp)
- EmergencyWithdrawal(user, 1000000000000000000000)

Property violated: User withdrew more than deposited during emergency state
Expected: withdrawal <= deposit
Actual: withdrawal = 1000 ETH, but user should only get 800 ETH after fees

Root cause: Emergency withdrawal bypasses the withdrawal request validation`,
    },
    {
      brokenProperty: "echidna_fee_calculation_accuracy",
      traces: `Call sequence that breaks fee calculation:
1. setFeeRate(250) -> set fee to 2.5% (250 basis points)
2. deposit(1000 ether) -> deposit 1000 ETH
3. setFeeRate(500) -> change fee to 5% (500 basis points)
4. withdraw(1000 ether) -> attempt to withdraw full amount

Event sequence:
- FeeRateChanged(250, 500)
- Deposit(user, 1000000000000000000000)
- WithdrawalProcessed(user, 975000000000000000000, 25000000000000000000)

Property violated: Fee calculation used old rate instead of current rate
Expected fee: 50 ETH (5% of 1000 ETH)
Actual fee: 25 ETH (2.5% of 1000 ETH)

Root cause: Fee calculation caches the rate at deposit time instead of using current rate`,
    },
    {
      brokenProperty: "echidna_reentrancy_protection",
      traces: `Call sequence that demonstrates reentrancy vulnerability:
1. deposit(500 ether) -> initial deposit
2. maliciousWithdraw() -> calls withdraw with malicious contract

Malicious contract execution:
- receive() function called during ETH transfer
- Calls withdraw() again before state update
- Drains additional funds

Event sequence:
- Deposit(maliciousContract, 500000000000000000000)
- WithdrawalStarted(maliciousContract, 500000000000000000000)
- receive() -> reentrancy triggered
- WithdrawalStarted(maliciousContract, 500000000000000000000) // Second withdrawal
- WithdrawalCompleted(maliciousContract, 1000000000000000000000)

Property violated: Single deposit allowed double withdrawal
Expected: 500 ETH withdrawal
Actual: 1000 ETH withdrawal

Root cause: State update happens after external call, allowing reentrancy`,
    },
    {
      brokenProperty: "echidna_access_control_admin",
      traces: `Call sequence that breaks admin access control:
1. transferOwnership(newAdmin) -> current admin transfers ownership
2. acceptOwnership() -> new admin accepts ownership
3. setFeeRate(1000) -> old admin still able to set fees

Event sequence:
- OwnershipTransferred(oldAdmin, newAdmin)
- FeeRateChanged(250, 1000) // Called by oldAdmin

Property violated: Old admin retained privileges after ownership transfer
Expected: Only newAdmin should be able to call admin functions
Actual: oldAdmin can still call setFeeRate()

Root cause: Access control modifier checks outdated admin mapping`,
    },
    {
      brokenProperty: "echidna_withdrawal_queue_ordering",
      traces: `Call sequence that breaks withdrawal queue FIFO ordering:
1. requestWithdrawal(user1, 100 ether) -> first request at timestamp T
2. requestWithdrawal(user2, 200 ether) -> second request at timestamp T+1
3. requestWithdrawal(user3, 150 ether) -> third request at timestamp T+2
4. processWithdrawals(2) -> process 2 withdrawals

Event sequence:
- WithdrawalRequested(user1, 100 ether, T)
- WithdrawalRequested(user2, 200 ether, T+1)
- WithdrawalRequested(user3, 150 ether, T+2)
- WithdrawalProcessed(user3, 150 ether) // Wrong order!
- WithdrawalProcessed(user2, 200 ether) // Wrong order!

Property violated: Withdrawals processed out of FIFO order
Expected order: user1, user2, user3
Actual order: user3, user2, user1

Root cause: Withdrawal queue uses incorrect sorting mechanism`,
    },
  ],
  progress: 100,
  eta: null,
};
